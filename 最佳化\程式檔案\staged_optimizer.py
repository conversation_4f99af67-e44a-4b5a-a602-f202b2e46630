import pandas as pd
import numpy as np
import itertools
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm
import time
import dask
from dask.distributed import Client, as_completed as dask_as_completed
from dask import delayed
import argparse
import os
import json

# 分階段參數優化器 - 先粗搜索再細化
# 階段1: 粗略搜索找到大致最佳區域
# 階段2: 在最佳區域周圍進行細化搜索

def load_and_process_data():
    """載入和處理數據"""
    try:
        df = pd.read_csv('lookingforthebest/eth_usdt_4h_8years_binance.csv', index_col='timestamp', parse_dates=True)
        df.columns = [col.lower() for col in df.columns]
        
        # 計算指標
        df['ema90'] = df['close'].ewm(span=90, adjust=False).mean()
        df['ema200'] = df['close'].ewm(span=200, adjust=False).mean()
        
        # 簡化的ADX計算
        high_low = df['high'] - df['low']
        high_close = abs(df['high'] - df['close'].shift(1))
        low_close = abs(df['low'] - df['close'].shift(1))
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        
        plus_dm = df['high'].diff()
        minus_dm = -df['low'].diff()
        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm < 0] = 0
        plus_dm[(plus_dm <= minus_dm)] = 0
        minus_dm[(minus_dm <= plus_dm)] = 0
        
        atr = tr.rolling(window=14).mean()
        plus_di = 100 * (plus_dm.rolling(window=14).mean() / atr)
        minus_di = 100 * (minus_dm.rolling(window=14).mean() / atr)
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        df['adx'] = dx.rolling(window=14).mean()
        
        df = df.dropna()
        print(f"數據載入完成，共 {len(df)} 根K線")
        return df
        
    except Exception as e:
        print(f"數據載入失敗: {e}")
        return None

def backtest_quarter(quarter_data, params, period):
    """對單個季度進行回測"""
    try:
        initial_capital = 10000
        capital = 10000
        position_type = None  # 'LONG' or 'SHORT' or None
        position_size = 0  # ETH數量
        entry_price = 0
        long_peak = None
        long_trail_stop_price = None
        is_long_trail_active = False
        short_trough = None
        short_trail_stop_price = None
        is_short_trail_active = False
        trade_log = []
        
        # 回測邏輯
        for _, row in quarter_data.iterrows():
            current_close = row['close']
            current_high = row['high']
            current_low = row['low']
            current_adx = row['adx']
            
            if position_type is None:
                # 開倉邏輯
                if (row['ema90'] > row['ema200'] and current_adx > params['adx_threshold'] and
                    current_close > row['ema90']):
                    # 開多單 - 用50%資金買入ETH（1倍槓桿）
                    invest_amount = capital * 0.5
                    position_size = invest_amount / current_close
                    entry_price = current_close
                    position_type = 'LONG'
                    long_peak = current_close
                    capital -= invest_amount  # 扣除投入資金
                    trade_log.append({'type': 'LONG_ENTRY', 'price': current_close})

                elif (row['ema90'] < row['ema200'] and current_adx > params['adx_threshold'] and
                      current_close < row['ema90']):
                    # 開空單 - 用50%資金做空（1倍槓桿）
                    invest_amount = capital * 0.5
                    position_size = invest_amount / current_close
                    entry_price = current_close
                    position_type = 'SHORT'
                    short_trough = current_close
                    capital -= invest_amount  # 扣除投入資金
                    trade_log.append({'type': 'SHORT_ENTRY', 'price': current_close})
            
            elif position_type == 'LONG':
                # 多單管理
                if current_high > long_peak:
                    long_peak = current_high

                if not is_long_trail_active and current_close > entry_price * (1 + params['long_trailing_activate_profit_percent']):
                    long_trail_stop_price = entry_price * (1 + params['long_trailing_min_profit_percent'])
                    is_long_trail_active = True

                if is_long_trail_active:
                    long_trail_stop_price = max(
                        long_trail_stop_price if long_trail_stop_price is not None else 0,
                        long_peak * (1 - params['long_trailing_pullback_percent'])
                    )

                long_trail_stop = is_long_trail_active and current_close <= long_trail_stop_price
                long_fixed_stop_loss_price = entry_price * (1 - params['long_fixed_stop_loss_percent'])
                long_stop_loss = current_close <= long_fixed_stop_loss_price

                if long_trail_stop or long_stop_loss:
                    # 平多單：賣出ETH獲得USDT
                    exit_value = position_size * current_close
                    profit_loss = exit_value - (position_size * entry_price)
                    capital += exit_value  # 收回賣出ETH的資金
                    trade_log.append({'type': 'LONG_EXIT', 'price': current_close, 'profit_loss': profit_loss})

                    # 重置倉位
                    position_type = None
                    position_size = 0
                    entry_price = 0
                    long_peak = None
                    long_trail_stop_price = None
                    is_long_trail_active = False
            
            elif position_type == 'SHORT':
                # 空單管理
                if current_low < short_trough:
                    short_trough = current_low

                if not is_short_trail_active and current_close < entry_price * (1 - params['short_trailing_activate_profit_percent']):
                    short_trail_stop_price = entry_price * (1 - params['short_trailing_min_profit_percent'])
                    is_short_trail_active = True

                if is_short_trail_active:
                    short_trail_stop_price = min(
                        short_trail_stop_price if short_trail_stop_price is not None else float('inf'),
                        short_trough * (1 + params['short_trailing_pullback_percent'])
                    )

                short_trail_stop = is_short_trail_active and current_close >= short_trail_stop_price
                short_fixed_stop_loss_price = entry_price * (1 + params['short_fixed_stop_loss_percent'])
                short_stop_loss = current_close >= short_fixed_stop_loss_price

                if short_trail_stop or short_stop_loss:
                    # 平空單：空單獲利 = (入場價 - 出場價) * 倉位大小
                    profit_loss = (entry_price - current_close) * position_size
                    capital += (position_size * entry_price) + profit_loss  # 歸還原始投入 + 獲利
                    trade_log.append({'type': 'SHORT_EXIT', 'price': current_close, 'profit_loss': profit_loss})

                    # 重置倉位
                    position_type = None
                    position_size = 0
                    entry_price = 0
                    short_trough = None
                    short_trail_stop_price = None
                    is_short_trail_active = False
        
        # 計算績效指標
        total_profit = capital - initial_capital
        total_trades = len([t for t in trade_log if 'EXIT' in t['type']])
        winning_trades = len([t for t in trade_log if 'EXIT' in t['type'] and t.get('profit_loss', 0) > 0])
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        return {
            'period': period,
            'total_profit': total_profit,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'final_capital': capital
        }
    
    except Exception:
        return {
            'period': period,
            'total_profit': -1000,
            'max_drawdown_percent': 100,
            'total_trades': 0,
            'win_rate': 0,
            'final_capital': 0
        }

def test_parameter_combination(args):
    """測試單個參數組合在所有季度的表現"""
    params, quarters = args
    
    results = []
    for period, quarter_data in quarters:
        result = backtest_quarter(quarter_data, params, period)
        results.append(result)
    
    # 計算整體表現
    total_profit = sum([r['total_profit'] for r in results])
    positive_quarters = len([r for r in results if r['total_profit'] > 0])
    avg_profit = total_profit / len(results)
    max_loss = min([r['total_profit'] for r in results])
    avg_win_rate = sum([r['win_rate'] for r in results]) / len(results)
    avg_trades = sum([r['total_trades'] for r in results]) / len(results)
    
    # 穩定性評分
    profit_consistency = positive_quarters / len(results)
    stability_score = profit_consistency * 0.4 + (avg_profit / 1000) * 0.3 + (avg_win_rate / 100) * 0.3
    
    return {
        'params': params,
        'stability_score': stability_score,
        'profit_consistency': profit_consistency,
        'positive_quarters': positive_quarters,
        'total_quarters': len(results),
        'avg_profit': avg_profit,
        'max_loss': max_loss,
        'avg_win_rate': avg_win_rate,
        'avg_trades': avg_trades,
        'total_profit': total_profit,
        'quarter_results': results
    }

def stage1_coarse_search():
    """階段1: 粗略搜索"""
    print("=== 階段1: 粗略搜索 ===")
    
    # 粗略參數範圍
    parameters = {
        'adx_threshold': [15, 18, 22, 25, 28],
        'long_fixed_stop_loss_percent': [0.01, 0.015, 0.02, 0.025, 0.03],
        'long_trailing_activate_profit_percent': [0.01, 0.02, 0.03],
        'long_trailing_pullback_percent': [0.02, 0.04, 0.06],
        'long_trailing_min_profit_percent': [0.005, 0.015, 0.025],
        'short_fixed_stop_loss_percent': [0.01, 0.015, 0.02, 0.025, 0.03],
        'short_trailing_activate_profit_percent': [0.01, 0.02, 0.03],
        'short_trailing_pullback_percent': [0.02, 0.04, 0.06],
        'short_trailing_min_profit_percent': [0.005, 0.015, 0.025],
    }
    
    return run_optimization(parameters, "stage1_coarse_results.csv")

def stage2_fine_search(best_params):
    """階段2: 在最佳參數周圍進行細化搜索"""
    print("=== 階段2: 細化搜索 ===")
    print(f"基於最佳參數進行細化: {best_params}")
    
    # 在最佳參數周圍創建細化範圍（縮小搜索範圍）
    parameters = {}

    # ADX閾值 ±2，步長1
    adx_center = best_params['adx_threshold']
    parameters['adx_threshold'] = list(range(max(15, adx_center-2), min(30, adx_center+3), 1))

    # 其他參數 ±0.003，步長0.001
    for key in ['long_fixed_stop_loss_percent', 'short_fixed_stop_loss_percent']:
        center = best_params[key]
        parameters[key] = list(np.arange(max(0.005, center-0.003), min(0.05, center+0.004), 0.001))

    for key in ['long_trailing_activate_profit_percent', 'short_trailing_activate_profit_percent']:
        center = best_params[key]
        parameters[key] = list(np.arange(max(0.005, center-0.003), min(0.05, center+0.004), 0.002))

    for key in ['long_trailing_pullback_percent', 'short_trailing_pullback_percent']:
        center = best_params[key]
        parameters[key] = list(np.arange(max(0.01, center-0.005), min(0.08, center+0.006), 0.002))

    for key in ['long_trailing_min_profit_percent', 'short_trailing_min_profit_percent']:
        center = best_params[key]
        parameters[key] = list(np.arange(max(0.002, center-0.003), min(0.05, center+0.004), 0.001))
    
    return run_optimization(parameters, "stage2_fine_results.csv")

def run_optimization(parameters, output_file):
    """執行優化"""
    # 載入數據
    df = load_and_process_data()
    if df is None:
        return None
    
    # 分割數據為季度
    quarters = []
    start_date = df.index[0]
    while start_date < df.index[-1]:
        end_date = start_date + pd.DateOffset(months=3)
        quarter_data = df[(df.index >= start_date) & (df.index < end_date)]
        if len(quarter_data) > 50:  # 確保有足夠數據
            quarters.append((f"{start_date.year}Q{(start_date.month-1)//3+1}", quarter_data))
        start_date = end_date
    
    print(f"數據分為 {len(quarters)} 個季度")
    
    # 生成參數組合
    keys = parameters.keys()
    all_combinations = list(itertools.product(*parameters.values()))
    print(f"總共 {len(all_combinations)} 個參數組合")
    
    # 準備任務
    tasks = []
    for combination in all_combinations:
        params = dict(zip(keys, combination))
        tasks.append((params, quarters))
    
    # 並行處理 - 保留2個核心給用戶使用
    cpu_count = max(1, multiprocessing.cpu_count() - 2)
    print(f"系統總核心數: {multiprocessing.cpu_count()}, 使用 {cpu_count} 個CPU核心進行並行計算（保留2個核心）")
    
    results = []
    with ProcessPoolExecutor(max_workers=cpu_count) as executor:
        futures = [executor.submit(test_parameter_combination, task) for task in tasks]
        
        for future in tqdm(as_completed(futures), total=len(futures), desc="優化進度"):
            result = future.result()
            results.append(result)
    
    # 排序結果
    results.sort(key=lambda x: x['stability_score'], reverse=True)
    
    # 顯示最佳結果
    print(f"\n=== 最佳穩定參數 ===")
    best = results[0]
    print(f"穩定性評分: {best['stability_score']:.2f}")
    print(f"獲利一致性: {best['profit_consistency']:.2%}")
    print(f"平均季度獲利: {best['avg_profit']:.2f} USDT")
    print(f"總交易次數: {sum([quarter['total_trades'] for quarter in best['quarter_results']])} 次")
    
    # 保存結果
    results_df = pd.DataFrame([{
        'rank': i+1,
        'stability_score': r['stability_score'],
        'profit_consistency': r['profit_consistency'],
        'avg_profit': r['avg_profit'],
        'total_trades': sum([quarter['total_trades'] for quarter in r['quarter_results']]),
        **r['params']
    } for i, r in enumerate(results)])
    
    results_df.to_csv(output_file, index=False)
    print(f"結果已保存到 {output_file}")
    
    return results[0]['params']

def main():
    """主函數"""
    print("=== 分階段參數優化器 ===")
    
    # 階段1: 粗略搜索
    best_coarse_params = stage1_coarse_search()
    
    if best_coarse_params:
        print(f"\n階段1完成，最佳粗略參數已找到")
        
        # 階段2: 細化搜索
        best_fine_params = stage2_fine_search(best_coarse_params)
        
        if best_fine_params:
            print(f"\n=== 最終最佳參數 ===")
            for key, value in best_fine_params.items():
                print(f"{key} = {value}")
            
            # 保存最終參數
            with open('best_parameters.json', 'w') as f:
                json.dump(best_fine_params, f, indent=2)
            print(f"\n最終參數已保存到 best_parameters.json")

if __name__ == "__main__":
    main()
