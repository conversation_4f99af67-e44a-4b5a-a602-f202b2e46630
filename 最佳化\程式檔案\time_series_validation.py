import pandas as pd
import numpy as np
from datetime import datetime
import itertools
import multiprocessing
import os
from tqdm import tqdm

def load_data(file_path='lookingforthebest/eth_usdt_4h_data.csv'):
    """載入歷史數據"""
    try:
        df = pd.read_csv(file_path, index_col='timestamp', parse_dates=True)
        df.columns = [col.lower() for col in df.columns]
        return df
    except FileNotFoundError:
        print(f"錯誤：找不到數據檔案 {file_path}")
        return pd.DataFrame()
    except Exception as e:
        print(f"載入數據時發生錯誤: {e}")
        return pd.DataFrame()

def calculate_ema(series, period):
    """計算指數移動平均線"""
    return series.ewm(span=period, adjust=False).mean()

def calculate_rsi(series, period=14):
    """計算RSI指標"""
    delta = series.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd(series, fast=12, slow=26, signal=9):
    """計算MACD指標"""
    ema_fast = calculate_ema(series, fast)
    ema_slow = calculate_ema(series, slow)
    macd = ema_fast - ema_slow
    macd_signal = calculate_ema(macd, signal)
    macd_histogram = macd - macd_signal
    return macd, macd_signal, macd_histogram

def calculate_adx(high, low, close, period=14):
    """計算ADX指標"""
    # 計算True Range
    tr1 = high - low
    tr2 = abs(high - close.shift(1))
    tr3 = abs(low - close.shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # 計算方向移動
    plus_dm = high.diff()
    minus_dm = -low.diff()
    
    plus_dm[plus_dm < 0] = 0
    minus_dm[minus_dm < 0] = 0
    
    # 當+DM > -DM時，-DM = 0；當-DM > +DM時，+DM = 0
    plus_dm[(plus_dm <= minus_dm)] = 0
    minus_dm[(minus_dm <= plus_dm)] = 0
    
    # 計算平滑的TR和DM
    tr_smooth = tr.rolling(window=period).mean()
    plus_dm_smooth = plus_dm.rolling(window=period).mean()
    minus_dm_smooth = minus_dm.rolling(window=period).mean()
    
    # 計算DI
    plus_di = 100 * (plus_dm_smooth / tr_smooth)
    minus_di = 100 * (minus_dm_smooth / tr_smooth)
    
    # 計算DX
    dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
    
    # 計算ADX
    adx = dx.rolling(window=period).mean()
    
    return adx, plus_di, minus_di

def calculate_indicators(df):
    """計算所有技術指標"""
    df = df.copy()
    
    # EMA指標
    df['ema90'] = calculate_ema(df['close'], 90)
    df['ema200'] = calculate_ema(df['close'], 200)
    
    # ADX指標
    adx, plus_di, minus_di = calculate_adx(df['high'], df['low'], df['close'])
    df['adx'] = adx
    df['plus_di'] = plus_di
    df['minus_di'] = minus_di
    
    # MACD指標
    macd, macd_signal, macd_histogram = calculate_macd(df['close'])
    df['macd'] = macd
    df['macd_signal'] = macd_signal
    df['macd_histogram'] = macd_histogram
    
    # RSI指標
    df['rsi'] = calculate_rsi(df['close'])
    
    return df.dropna()

def split_time_series_data(df, train_ratio=0.7):
    """按時間序列分割數據，避免未來信息洩漏"""
    total_length = len(df)
    train_length = int(total_length * train_ratio)
    
    train_data = df.iloc[:train_length].copy()
    test_data = df.iloc[train_length:].copy()
    
    print(f"數據分割結果：")
    print(f"總數據量：{total_length} 筆")
    print(f"訓練期：{len(train_data)} 筆 ({train_data.index[0]} 到 {train_data.index[-1]})")
    print(f"測試期：{len(test_data)} 筆 ({test_data.index[0]} 到 {test_data.index[-1]})")
    
    return train_data, test_data

class TimeSeriesTradingStrategy:
    def __init__(self, initial_capital=1000, default_qty_percent=70,
                 adx_threshold=17,
                 long_fixed_stop_loss_percent=0.02,
                 long_trailing_activate_profit_percent=0.02,
                 long_trailing_pullback_percent=0.04,
                 long_trailing_min_profit_percent=0.015,
                 short_fixed_stop_loss_percent=0.02,
                 short_trailing_activate_profit_percent=0.02,
                 short_trailing_pullback_percent=0.03,
                 short_trailing_min_profit_percent=0.01):
       
        self.initial_capital = initial_capital
        self.default_qty_percent = default_qty_percent
        self.current_capital = initial_capital
        self.position_size = 0  
        self.entry_price = 0    

        self.adx_threshold = adx_threshold  
       
        self.long_fixed_stop_loss_percent = long_fixed_stop_loss_percent
        self.long_trailing_activate_profit_percent = long_trailing_activate_profit_percent
        self.long_trailing_pullback_percent = long_trailing_pullback_percent
        self.long_trailing_min_profit_percent = long_trailing_min_profit_percent

        self.short_fixed_stop_loss_percent = short_fixed_stop_loss_percent
        self.short_trailing_activate_profit_percent = short_trailing_activate_profit_percent
        self.short_trailing_pullback_percent = short_trailing_pullback_percent
        self.short_trailing_min_profit_percent = short_trailing_min_profit_percent

        self.long_entry_price = None
        self.long_peak = None
        self.long_trail_stop_price = None
        self.is_long_trail_active = False

        self.short_entry_price = None
        self.short_trough = None
        self.short_trail_stop_price = None
        self.is_short_trail_active = False

        self.trade_log = []
        self.start_date = datetime(2022, 12, 31, 0, 0)

        self.peak_capital = initial_capital
        self.max_drawdown = 0.0            

    def reset(self):
        """重置策略狀態，用於新的回測"""
        self.current_capital = self.initial_capital
        self.position_size = 0
        self.entry_price = 0
        self.long_entry_price = None
        self.long_peak = None
        self.long_trail_stop_price = None
        self.is_long_trail_active = False
        self.short_entry_price = None
        self.short_trough = None
        self.short_trail_stop_price = None
        self.is_short_trail_active = False
        self.trade_log = []
        self.peak_capital = self.initial_capital
        self.max_drawdown = 0.0

    def process_bar(self, current_bar):
        current_time = current_bar.name
        current_close = current_bar['close']
        current_high = current_bar['high']
        current_low = current_bar['low']
        current_adx = current_bar['adx']

        if pd.isna(current_bar['ema90']) or pd.isna(current_bar['ema200']) or pd.isna(current_adx):
            return

        strong_trend = current_adx > self.adx_threshold
        is_in_date_range = current_time >= self.start_date

        long_entry_condition = (
            current_close > current_bar['ema90'] and
            current_low > current_bar['ema90'] and
            current_close > current_bar['ema200'] and
            strong_trend
        )
        short_entry_condition = (
            current_close < current_bar['ema90'] and
            current_high < current_bar['ema90'] and
            current_close < current_bar['ema200'] and
            strong_trend
        )

        # 多單邏輯
        if long_entry_condition and is_in_date_range and self.position_size == 0:
            trade_qty_unrounded = (self.current_capital * self.default_qty_percent / 100) / current_close
            trade_qty = round(trade_qty_unrounded, 3)
           
            if trade_qty >= 0.001:
                self.position_size = trade_qty
                self.entry_price = current_close
                self.long_entry_price = current_close
                self.long_peak = current_close
                self.long_trail_stop_price = None
                self.is_long_trail_active = False
                
                self.trade_log.append({
                    'time': current_time, 'type': 'LONG_ENTRY', 'price': current_close, 'qty': trade_qty,
                    'current_position_size': self.position_size, 'current_capital': self.current_capital
                })

        if self.position_size > 0:
            self.long_peak = max(self.long_peak, current_high)

            if current_close > self.long_entry_price * (1 + self.long_trailing_activate_profit_percent) and not self.is_long_trail_active:
                self.long_trail_stop_price = self.long_entry_price * (1 + self.long_trailing_min_profit_percent)
                self.is_long_trail_active = True

            if self.is_long_trail_active:
                self.long_trail_stop_price = max(
                    self.long_trail_stop_price if self.long_trail_stop_price is not None else 0,
                    self.long_peak * (1 - self.long_trailing_pullback_percent)
                )
           
            long_trail_stop = self.is_long_trail_active and current_close < self.long_trail_stop_price
            long_fixed_stop_loss_price = self.long_entry_price * (1 - self.long_fixed_stop_loss_percent)
            long_stop_loss = current_close < long_fixed_stop_loss_price

            if (long_trail_stop or long_stop_loss) and is_in_date_range:
                profit_loss = (current_close - self.entry_price) * self.position_size
                self.current_capital += profit_loss

                self.position_size = 0
                self.entry_price = 0
                self.long_entry_price = None
                self.long_peak = None
                self.long_trail_stop_price = None
                self.is_long_trail_active = False
               
                exit_reason = "TRAIL_STOP" if long_trail_stop else "FIXED_STOP"
               
                self.trade_log.append({
                    'time': current_time, 'type': 'LONG_EXIT', 'price': current_close, 'profit_loss': profit_loss,
                    'current_position_size': self.position_size, 'current_capital': self.current_capital, 
                    'reason': exit_reason
                })

        # 空單邏輯
        if short_entry_condition and is_in_date_range and self.position_size == 0:
            trade_qty_unrounded = (self.current_capital * self.default_qty_percent / 100) / current_close
            trade_qty = round(trade_qty_unrounded, 3)

            if trade_qty >= 0.001:
                self.position_size = -trade_qty
                self.entry_price = current_close
                self.short_entry_price = current_close
                self.short_trough = current_close
                self.short_trail_stop_price = None
                self.is_short_trail_active = False
                
                self.trade_log.append({
                    'time': current_time, 'type': 'SHORT_ENTRY', 'price': current_close, 'qty': trade_qty,
                    'current_position_size': self.position_size, 'current_capital': self.current_capital
                })

        if self.position_size < 0:
            self.short_trough = min(self.short_trough, current_low)

            if current_close < self.short_entry_price * (1 - self.short_trailing_activate_profit_percent) and not self.is_short_trail_active:
                self.short_trail_stop_price = self.short_entry_price * (1 - self.short_trailing_min_profit_percent)
                self.is_short_trail_active = True

            if self.is_short_trail_active:
                self.short_trail_stop_price = min(
                    self.short_trail_stop_price if self.short_trail_stop_price is not None else float('inf'),
                    self.short_trough * (1 + self.short_trailing_pullback_percent)
                )
           
            short_trail_stop = self.is_short_trail_active and current_close > self.short_trail_stop_price
            short_fixed_stop_loss_price = self.short_entry_price * (1 + self.short_fixed_stop_loss_percent)
            short_stop_loss = current_close > short_fixed_stop_loss_price

            if (short_trail_stop or short_stop_loss) and is_in_date_range:
                profit_loss = (self.entry_price - current_close) * abs(self.position_size)
                self.current_capital += profit_loss

                self.position_size = 0
                self.entry_price = 0
                self.short_entry_price = None
                self.short_trough = None
                self.short_trail_stop_price = None
                self.is_short_trail_active = False
               
                exit_reason = "TRAIL_STOP" if short_trail_stop else "FIXED_STOP"

                self.trade_log.append({
                    'time': current_time, 'type': 'SHORT_EXIT', 'price': current_close, 'profit_loss': profit_loss,
                    'current_position_size': self.position_size, 'current_capital': self.current_capital, 
                    'reason': exit_reason
                })

        # 更新資金和回撤
        total_current_value = self.current_capital
        if self.position_size > 0:
            unrealized_pnl = (current_close - self.entry_price) * self.position_size
            total_current_value += unrealized_pnl
        elif self.position_size < 0:
            unrealized_pnl = (self.entry_price - current_close) * abs(self.position_size)
            total_current_value += unrealized_pnl
       
        self.peak_capital = max(self.peak_capital, total_current_value)
       
        if self.peak_capital > 0:
            current_drawdown = (self.peak_capital - total_current_value) / self.peak_capital
            self.max_drawdown = max(self.max_drawdown, current_drawdown)

def run_backtest_with_params(params_tuple):
    """執行單一參數組合的回測"""
    params, df_data, initial_capital, default_qty_percent = params_tuple
    
    strategy = TimeSeriesTradingStrategy(
        initial_capital=initial_capital,
        default_qty_percent=default_qty_percent,
        **params
    )
    
    for _, row in df_data.iterrows():
        strategy.process_bar(row)
    
    # 計算績效指標
    total_profit = strategy.current_capital - initial_capital
    max_dd_percent = strategy.max_drawdown * 100
    
    # 計算交易統計
    exit_trades = [t for t in strategy.trade_log if 'EXIT' in t['type']]
    total_trades = len(exit_trades)
    winning_trades = sum(1 for t in exit_trades if t['profit_loss'] > 0)
    win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
    
    # 計算夏普比率（簡化版）
    if total_trades > 0:
        returns = [t['profit_loss'] for t in exit_trades]
        avg_return = np.mean(returns)
        std_return = np.std(returns)
        sharpe_ratio = avg_return / std_return if std_return > 0 else 0
    else:
        sharpe_ratio = 0
    
    return {
        'params': params,
        'total_profit': total_profit,
        'max_drawdown_percent': max_dd_percent,
        'total_trades': total_trades,
        'win_rate': win_rate,
        'sharpe_ratio': sharpe_ratio,
        'final_capital': strategy.current_capital
    }

def main():
    """主函數：執行時間序列分割驗證"""
    print("=== 時間序列分割驗證 ===")
    
    # 1. 載入和處理數據
    df_data = load_data()
    if df_data.empty:
        print("無法載入數據，程序結束")
        return
    
    print(f"載入數據成功，共 {len(df_data)} 筆記錄")
    print(f"數據時間範圍：{df_data.index[0]} 到 {df_data.index[-1]}")
    
    # 2. 計算指標
    print("正在計算技術指標...")
    df_processed = calculate_indicators(df_data.copy())
    print(f"指標計算完成，有效數據 {len(df_processed)} 筆")
    
    # 3. 時間序列分割
    train_data, test_data = split_time_series_data(df_processed, train_ratio=0.7)
    
    # 保存分割後的數據
    train_data.to_csv('train_data.csv')
    test_data.to_csv('test_data.csv')
    print("訓練和測試數據已保存")
    
    return train_data, test_data

if __name__ == "__main__":
    main()
