import pandas as pd
import numpy as np
import itertools
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm
import time

# 快速穩定性優化器 - 根據更新後的交易邏輯進行參數最佳化
# 主要更新：
# 1. 停損判斷使用收盤價而非最低/最高價 (使用 <= 和 >= 觸發條件)
# 2. 擴展參數搜索範圍以適應新的交易邏輯
# 3. 保持與 eth_strategy_4h_autotrading.py 的邏輯一致性

def load_and_process_data():
    """載入和處理數據"""
    try:
        df = pd.read_csv('lookingforthebest/eth_usdt_4h_8years_binance.csv', index_col='timestamp', parse_dates=True)
        df.columns = [col.lower() for col in df.columns]
        
        # 計算指標
        df['ema90'] = df['close'].ewm(span=90, adjust=False).mean()
        df['ema200'] = df['close'].ewm(span=200, adjust=False).mean()
        
        # 簡化的ADX計算
        high_low = df['high'] - df['low']
        high_close = abs(df['high'] - df['close'].shift(1))
        low_close = abs(df['low'] - df['close'].shift(1))
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        
        plus_dm = df['high'].diff()
        minus_dm = -df['low'].diff()
        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm < 0] = 0
        plus_dm[(plus_dm <= minus_dm)] = 0
        minus_dm[(minus_dm <= plus_dm)] = 0
        
        tr_smooth = tr.rolling(window=14).mean()
        plus_dm_smooth = plus_dm.rolling(window=14).mean()
        minus_dm_smooth = minus_dm.rolling(window=14).mean()
        
        plus_di = 100 * (plus_dm_smooth / tr_smooth)
        minus_di = 100 * (minus_dm_smooth / tr_smooth)
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        df['adx'] = dx.rolling(window=14).mean()
        
        return df.dropna()
    except Exception as e:
        print(f"數據載入失敗: {e}")
        return pd.DataFrame()

def split_by_quarters(df):
    """按季度分割數據"""
    quarters = []
    df['year'] = df.index.year
    df['quarter'] = df.index.quarter
    
    for year in df['year'].unique():
        for quarter in [1, 2, 3, 4]:
            quarter_data = df[(df['year'] == year) & (df['quarter'] == quarter)].copy()
            if len(quarter_data) > 100:
                quarter_data = quarter_data.drop(['year', 'quarter'], axis=1)
                quarters.append({
                    'period': f"{year}Q{quarter}",
                    'data': quarter_data
                })
    return quarters

def run_backtest_single_quarter(args):
    """執行單季度回測（多進程函數）"""
    params, quarter_data, period = args
    
    try:
        # 初始化
        capital = 1000
        position_size = 0
        entry_price = 0
        long_entry_price = None
        long_peak = None
        long_trail_stop_price = None
        is_long_trail_active = False
        short_entry_price = None
        short_trough = None
        short_trail_stop_price = None
        is_short_trail_active = False
        trade_log = []
        peak_capital = capital
        max_drawdown = 0.0
        
        # 回測邏輯
        for _, row in quarter_data.iterrows():
            current_close = row['close']
            current_high = row['high']
            current_low = row['low']
            current_adx = row['adx']
            
            if pd.isna(row['ema90']) or pd.isna(row['ema200']) or pd.isna(current_adx):
                continue
            
            strong_trend = current_adx > params['adx_threshold']
            
            long_entry_condition = (
                current_close > row['ema90'] and
                current_low > row['ema90'] and
                current_close > row['ema200'] and
                strong_trend
            )
            short_entry_condition = (
                current_close < row['ema90'] and
                current_high < row['ema90'] and
                current_close < row['ema200'] and
                strong_trend
            )
            
            # 多單邏輯
            if long_entry_condition and position_size == 0:
                trade_qty = round((capital * 70 / 100) / current_close, 3)
                if trade_qty >= 0.001:
                    position_size = trade_qty
                    entry_price = current_close
                    long_entry_price = current_close
                    long_peak = current_close
                    long_trail_stop_price = None
                    is_long_trail_active = False
                    trade_log.append({'type': 'LONG_ENTRY', 'price': current_close})
            
            if position_size > 0:
                long_peak = max(long_peak, current_high)
                
                if current_close > long_entry_price * (1 + params['long_trailing_activate_profit_percent']) and not is_long_trail_active:
                    long_trail_stop_price = long_entry_price * (1 + params['long_trailing_min_profit_percent'])
                    is_long_trail_active = True
                
                if is_long_trail_active:
                    long_trail_stop_price = max(
                        long_trail_stop_price if long_trail_stop_price is not None else 0,
                        long_peak * (1 - params['long_trailing_pullback_percent'])
                    )
                
                long_trail_stop = is_long_trail_active and current_close <= long_trail_stop_price
                long_fixed_stop_loss_price = long_entry_price * (1 - params['long_fixed_stop_loss_percent'])
                long_stop_loss = current_close <= long_fixed_stop_loss_price
                
                if long_trail_stop or long_stop_loss:
                    profit_loss = (current_close - entry_price) * position_size
                    capital += profit_loss
                    trade_log.append({'type': 'LONG_EXIT', 'price': current_close, 'profit_loss': profit_loss})
                    
                    position_size = 0
                    entry_price = 0
                    long_entry_price = None
                    long_peak = None
                    long_trail_stop_price = None
                    is_long_trail_active = False
            
            # 空單邏輯
            if short_entry_condition and position_size == 0:
                trade_qty = round((capital * 70 / 100) / current_close, 3)
                if trade_qty >= 0.001:
                    position_size = -trade_qty
                    entry_price = current_close
                    short_entry_price = current_close
                    short_trough = current_close
                    short_trail_stop_price = None
                    is_short_trail_active = False
                    trade_log.append({'type': 'SHORT_ENTRY', 'price': current_close})
            
            if position_size < 0:
                short_trough = min(short_trough, current_low)
                
                if current_close < short_entry_price * (1 - params['short_trailing_activate_profit_percent']) and not is_short_trail_active:
                    short_trail_stop_price = short_entry_price * (1 - params['short_trailing_min_profit_percent'])
                    is_short_trail_active = True
                
                if is_short_trail_active:
                    short_trail_stop_price = min(
                        short_trail_stop_price if short_trail_stop_price is not None else float('inf'),
                        short_trough * (1 + params['short_trailing_pullback_percent'])
                    )
                
                short_trail_stop = is_short_trail_active and current_close >= short_trail_stop_price
                short_fixed_stop_loss_price = short_entry_price * (1 + params['short_fixed_stop_loss_percent'])
                short_stop_loss = current_close >= short_fixed_stop_loss_price
                
                if short_trail_stop or short_stop_loss:
                    profit_loss = (entry_price - current_close) * abs(position_size)
                    capital += profit_loss
                    trade_log.append({'type': 'SHORT_EXIT', 'price': current_close, 'profit_loss': profit_loss})
                    
                    position_size = 0
                    entry_price = 0
                    short_entry_price = None
                    short_trough = None
                    short_trail_stop_price = None
                    is_short_trail_active = False
            
            # 更新回撤
            total_value = capital
            if position_size > 0:
                total_value += (current_close - entry_price) * position_size
            elif position_size < 0:
                total_value += (entry_price - current_close) * abs(position_size)
            
            peak_capital = max(peak_capital, total_value)
            if peak_capital > 0:
                current_drawdown = (peak_capital - total_value) / peak_capital
                max_drawdown = max(max_drawdown, current_drawdown)
        
        # 計算結果
        total_profit = capital - 1000
        exit_trades = [t for t in trade_log if 'EXIT' in t['type']]
        total_trades = len(exit_trades)
        winning_trades = sum(1 for t in exit_trades if t['profit_loss'] > 0)
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        return {
            'period': period,
            'total_profit': total_profit,
            'max_drawdown_percent': max_drawdown * 100,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'final_capital': capital
        }
    
    except Exception:
        return {
            'period': period,
            'total_profit': -1000,
            'max_drawdown_percent': 100,
            'total_trades': 0,
            'win_rate': 0,
            'final_capital': 0
        }

def test_parameter_combination(args):
    """測試單個參數組合在所有季度的表現"""
    params, quarters = args
    
    # 為每個季度準備任務
    quarter_tasks = [(params, quarter['data'], quarter['period']) for quarter in quarters]
    
    # 順序執行各季度（避免嵌套多進程）
    quarterly_results = []
    for task in quarter_tasks:
        result = run_backtest_single_quarter(task)
        if result['total_profit'] > -1000:  # 排除失敗的結果
            quarterly_results.append(result)
    
    if not quarterly_results:
        return None
    
    # 計算穩定性指標
    profits = [r['total_profit'] for r in quarterly_results]
    win_rates = [r['win_rate'] for r in quarterly_results]
    trade_counts = [r['total_trades'] for r in quarterly_results]
    
    avg_profit = np.mean(profits)
    profit_std = np.std(profits)
    positive_quarters = sum(1 for p in profits if p > 0)
    max_loss = min(profits)
    profit_consistency = positive_quarters / len(quarterly_results)
    risk_adjusted_return = avg_profit / profit_std if profit_std > 0 else 0
    avg_win_rate = np.mean(win_rates)
    avg_trades = np.mean(trade_counts) if quarterly_results else 0
    
    # 穩定性評分（重點：避免虧損）
    stability_score = (
        profit_consistency * 50 +  # 獲利一致性最重要
        max(0, avg_profit) * 0.05 +  # 平均獲利
        max(0, -max_loss) * 0.05 +  # 最大虧損控制
        risk_adjusted_return * 15 +  # 風險調整回報
        avg_win_rate * 0.3  # 平均勝率
    )
    
    return {
        'params': params,
        'stability_score': stability_score,
        'avg_profit': avg_profit,
        'profit_std': profit_std,
        'positive_quarters': positive_quarters,
        'total_quarters': len(quarterly_results),
        'max_loss': max_loss,
        'profit_consistency': profit_consistency,
        'risk_adjusted_return': risk_adjusted_return,
        'avg_win_rate': avg_win_rate,
        'avg_trades': avg_trades,
        'quarterly_results': quarterly_results
    }

def main():
    """主函數：多核心穩定性參數優化"""
    print("=== 多核心穩定性參數優化 ===")
    print(f"檢測到 {multiprocessing.cpu_count()} 個CPU核心，將使用 18 個進程")
    
    # 載入數據
    print("載入和處理數據...")
    df_processed = load_and_process_data()
    if df_processed.empty:
        return
    
    print(f"數據處理完成，共 {len(df_processed)} 筆記錄")
    
    # 按季度分割
    quarters = split_by_quarters(df_processed)
    print(f"數據分為 {len(quarters)} 個季度")
    
    # 定義參數範圍 - 根據更新後的交易邏輯調整
    parameters = {
        'adx_threshold': np.arange(15,30,1),
        'long_fixed_stop_loss_percent': np.arange(0.01,0.04,0.003),
        'long_trailing_activate_profit_percent': np.arange(0.01,0.04,0.005),
        'long_trailing_pullback_percent': np.arange(0.01,0.07,0.005),
        'long_trailing_min_profit_percent': np.arange(0.005,0.05,0.005),
        'short_fixed_stop_loss_percent': np.arange(0.01,0.05,0.003),
        'short_trailing_activate_profit_percent': np.arange(0.01,0.04,0.005),
        'short_trailing_pullback_percent': np.arange(0.01,0.07,0.005),
        'short_trailing_min_profit_percent': np.arange(0.005,0.05,0.005),
    }
    
    # 生成參數組合
    keys = parameters.keys()
    all_combinations = list(itertools.product(*parameters.values()))
    print(f"總共 {len(all_combinations)} 個參數組合")
    
    # 準備任務
    tasks = []
    for combo in all_combinations:
        params = dict(zip(keys, combo))
        tasks.append((params, quarters))
    
    # 多進程執行
    print("開始多核心優化...")
    start_time = time.time()
    
    results = []
    with ProcessPoolExecutor(max_workers=18) as executor:
        for result in tqdm(executor.map(test_parameter_combination, tasks), total=len(tasks), desc="優化進度"):
            if result is not None:
                results.append(result)
    
    end_time = time.time()
    print(f"優化完成！耗時: {end_time - start_time:.2f} 秒")
    print(f"成功測試 {len(results)} 個參數組合")
    
    if not results:
        print("沒有成功的參數組合")
        return
    
    # 排序並顯示結果
    results.sort(key=lambda x: x['stability_score'], reverse=True)
    
    print(f"\n=== 最佳穩定參數 ===")
    best = results[0]
    print(f"穩定性評分: {best['stability_score']:.2f}")
    print(f"獲利一致性: {best['profit_consistency']:.2%} ({best['positive_quarters']}/{best['total_quarters']} 季度)")
    print(f"平均季度獲利: {best['avg_profit']:.2f} USDT")
    print(f"最大季度虧損: {best['max_loss']:.2f} USDT")
    print(f"平均勝率: {best['avg_win_rate']:.2f}%")
    print(f"平均每季度交易次數: {best['avg_trades']:.1f} 次")
    print(f"總交易次數: {sum([quarter['total_trades'] for quarter in best['quarter_results']])} 次")
    
    print(f"\n建議參數:")
    params = best['params']
    for key, value in params.items():
        print(f"{key}={value},")
    
    # 保存結果
    results_df = pd.DataFrame([{
        'rank': i+1,
        'stability_score': r['stability_score'],
        'profit_consistency': r['profit_consistency'],
        'avg_profit': r['avg_profit'],
        'max_loss': r['max_loss'],
        'avg_win_rate': r['avg_win_rate'],
        'avg_trades': r['avg_trades'],
        'total_trades': sum([quarter['total_trades'] for quarter in r['quarter_results']]),  # 總交易次數
        **r['params']
    } for i, r in enumerate(results)])
    
    results_df.to_csv('fast_stability_results.csv', index=False)
    print(f"\n結果已保存到 fast_stability_results.csv")

if __name__ == "__main__":
    main()
