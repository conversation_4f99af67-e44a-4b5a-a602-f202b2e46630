import pandas as pd
import numpy as np
import itertools
import multiprocessing
from tqdm import tqdm
import time
import dask
from dask.distributed import Client, as_completed as dask_as_completed
from dask import delayed
import argparse
import os
import json

# Dask分散式參數優化器 - 支援多機器叢集運算
# 基於staged_optimizer.py的第二階段優化邏輯

def load_and_process_data():
    """載入和處理數據"""
    try:
        df = pd.read_csv('../../下載歷史K棒/lookingforthebest/eth_usdt_4h_8years_binance.csv', index_col='timestamp', parse_dates=True)
        df.columns = [col.lower() for col in df.columns]
        
        # 計算指標
        df['ema90'] = df['close'].ewm(span=90, adjust=False).mean()
        df['ema200'] = df['close'].ewm(span=200, adjust=False).mean()
        
        # 簡化的ADX計算
        high_low = df['high'] - df['low']
        high_close = abs(df['high'] - df['close'].shift(1))
        low_close = abs(df['low'] - df['close'].shift(1))
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        
        plus_dm = df['high'].diff()
        minus_dm = -df['low'].diff()
        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm < 0] = 0
        plus_dm[(plus_dm <= minus_dm)] = 0
        minus_dm[(minus_dm <= plus_dm)] = 0
        
        tr_smooth = tr.rolling(window=14).mean()
        plus_dm_smooth = plus_dm.rolling(window=14).mean()
        minus_dm_smooth = minus_dm.rolling(window=14).mean()
        
        plus_di = 100 * (plus_dm_smooth / tr_smooth)
        minus_di = 100 * (minus_dm_smooth / tr_smooth)
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        df['adx'] = dx.rolling(window=14).mean()
        
        return df.dropna()
    except Exception as e:
        print(f"數據載入失敗: {e}")
        return pd.DataFrame()

def split_by_quarters(df):
    """按季度分割數據"""
    quarters = []
    df['year'] = df.index.year
    df['quarter'] = df.index.quarter
    
    for year in df['year'].unique():
        for quarter in [1, 2, 3, 4]:
            quarter_data = df[(df['year'] == year) & (df['quarter'] == quarter)].copy()
            if len(quarter_data) > 100:
                quarter_data = quarter_data.drop(['year', 'quarter'], axis=1)
                quarters.append({
                    'period': f"{year}Q{quarter}",
                    'data': quarter_data
                })
    return quarters

def run_backtest_single_quarter(params, quarter_data, period):
    """執行單季度回測（Dask分散式函數）"""
    try:
        # 初始化
        capital = 10000  # 起始資金
        position_size = 0
        entry_price = 0
        position_direction = None  # 'long' or 'short'
        
        # 多單相關變數
        long_entry_price = None
        long_peak = None
        long_trail_stop_price = None
        is_long_trail_active = False
        
        # 空單相關變數
        short_entry_price = None
        short_trough = None
        short_trail_stop_price = None
        is_short_trail_active = False
        
        trade_log = []
        
        # 回測邏輯
        for _, row in quarter_data.iterrows():
            current_close = row['close']
            current_high = row['high']
            current_low = row['low']
            current_adx = row['adx']
            
            if pd.isna(row['ema90']) or pd.isna(row['ema200']) or pd.isna(current_adx):
                continue
            
            strong_trend = current_adx > params['adx_threshold']
            
            # 進場條件
            long_entry_condition = (
                current_close > row['ema90'] and
                current_low > row['ema90'] and
                current_close > row['ema200'] and
                strong_trend
            )
            short_entry_condition = (
                current_close < row['ema90'] and
                current_high < row['ema90'] and
                current_close < row['ema200'] and
                strong_trend
            )
            
            # 多單邏輯
            if long_entry_condition and position_size == 0:
                invest_amount = capital * 0.5  # 50%資金投入
                position_size = invest_amount / current_close
                capital -= invest_amount  # 扣除投入資金
                entry_price = current_close
                position_direction = 'long'
                
                long_entry_price = current_close
                long_peak = current_close
                long_trail_stop_price = None
                is_long_trail_active = False
                trade_log.append({'type': 'LONG_ENTRY', 'price': current_close})
            
            # 多單出場邏輯
            if position_direction == 'long' and position_size > 0:
                long_peak = max(long_peak, current_high)
                
                # 追蹤止損激活
                if current_close > long_entry_price * (1 + params['long_trailing_activate_profit_percent']) and not is_long_trail_active:
                    long_trail_stop_price = long_entry_price * (1 + params['long_trailing_min_profit_percent'])
                    is_long_trail_active = True
                
                # 更新追蹤止損價格
                if is_long_trail_active:
                    long_trail_stop_price = max(
                        long_trail_stop_price if long_trail_stop_price is not None else 0,
                        long_peak * (1 - params['long_trailing_pullback_percent'])
                    )
                
                # 止損條件檢查
                long_trail_stop_triggered = is_long_trail_active and current_close <= long_trail_stop_price
                long_fixed_stop_loss_price = long_entry_price * (1 - params['long_fixed_stop_loss_percent'])
                long_fixed_stop_loss_triggered = current_close <= long_fixed_stop_loss_price
                
                if long_trail_stop_triggered or long_fixed_stop_loss_triggered:
                    # 平倉
                    exit_value = position_size * current_close
                    profit_loss = exit_value - (position_size * entry_price)
                    capital += exit_value  # 返還資金
                    
                    trade_log.append({'type': 'LONG_EXIT', 'price': current_close, 'profit_loss': profit_loss})
                    
                    # 重置變數
                    position_size = 0
                    entry_price = 0
                    position_direction = None
                    long_entry_price = None
                    long_peak = None
                    long_trail_stop_price = None
                    is_long_trail_active = False
            
            # 空單邏輯
            if short_entry_condition and position_size == 0:
                invest_amount = capital * 0.5  # 50%資金投入
                position_size = invest_amount / current_close
                capital -= invest_amount  # 扣除投入資金
                entry_price = current_close
                position_direction = 'short'
                
                short_entry_price = current_close
                short_trough = current_close
                short_trail_stop_price = None
                is_short_trail_active = False
                trade_log.append({'type': 'SHORT_ENTRY', 'price': current_close})
            
            # 空單出場邏輯
            if position_direction == 'short' and position_size > 0:
                short_trough = min(short_trough, current_low)
                
                # 追蹤止損激活
                if current_close < short_entry_price * (1 - params['short_trailing_activate_profit_percent']) and not is_short_trail_active:
                    short_trail_stop_price = short_entry_price * (1 - params['short_trailing_min_profit_percent'])
                    is_short_trail_active = True
                
                # 更新追蹤止損價格
                if is_short_trail_active:
                    short_trail_stop_price = min(
                        short_trail_stop_price if short_trail_stop_price is not None else float('inf'),
                        short_trough * (1 + params['short_trailing_pullback_percent'])
                    )
                
                # 止損條件檢查
                short_trail_stop_triggered = is_short_trail_active and current_close >= short_trail_stop_price
                short_fixed_stop_loss_price = short_entry_price * (1 + params['short_fixed_stop_loss_percent'])
                short_fixed_stop_loss_triggered = current_close >= short_fixed_stop_loss_price
                
                if short_trail_stop_triggered or short_fixed_stop_loss_triggered:
                    # 平倉
                    profit_loss = (entry_price - current_close) * position_size
                    capital += (position_size * entry_price) + profit_loss  # 返還本金+獲利
                    
                    trade_log.append({'type': 'SHORT_EXIT', 'price': current_close, 'profit_loss': profit_loss})
                    
                    # 重置變數
                    position_size = 0
                    entry_price = 0
                    position_direction = None
                    short_entry_price = None
                    short_trough = None
                    short_trail_stop_price = None
                    is_short_trail_active = False
        
        # 計算結果
        total_profit = capital - 10000
        exit_trades = [t for t in trade_log if 'EXIT' in t['type']]
        total_trades = len(exit_trades)
        winning_trades = sum(1 for t in exit_trades if t['profit_loss'] > 0)
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        return {
            'period': period,
            'total_profit': total_profit,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'final_capital': capital
        }
    
    except Exception as e:
        return {
            'period': period,
            'total_profit': -10000,
            'total_trades': 0,
            'win_rate': 0,
            'final_capital': 0
        }

@delayed
def test_parameter_combination_dask(params, quarters):
    """使用Dask delayed裝飾器的參數測試函數"""
    quarterly_results = []
    
    for quarter in quarters:
        result = run_backtest_single_quarter(params, quarter['data'], quarter['period'])
        if result['total_profit'] > -10000:  # 排除失敗的結果
            quarterly_results.append(result)
    
    if not quarterly_results:
        return None
    
    # 計算穩定性指標
    profits = [r['total_profit'] for r in quarterly_results]
    win_rates = [r['win_rate'] for r in quarterly_results]
    trade_counts = [r['total_trades'] for r in quarterly_results]
    
    avg_profit = np.mean(profits)
    profit_std = np.std(profits) if len(profits) > 1 else 0
    positive_quarters = sum(1 for p in profits if p > 0)
    profit_consistency = positive_quarters / len(quarterly_results)
    avg_win_rate = np.mean(win_rates)
    total_trades = sum(trade_counts)
    
    # 穩定性評分
    stability_score = (
        profit_consistency * 0.4 +  # 獲利一致性
        (avg_profit / 10000) * 0.3 +  # 平均獲利率
        (avg_win_rate / 100) * 0.2 +  # 勝率
        (total_trades / len(quarterly_results) / 50) * 0.1  # 交易頻率適中
    )
    
    return {
        'params': params,
        'stability_score': stability_score,
        'avg_profit': avg_profit,
        'profit_consistency': profit_consistency,
        'avg_win_rate': avg_win_rate,
        'total_trades': total_trades,
        'quarterly_results': quarterly_results
    }

def generate_stage2_parameters(best_stage1_params):
    """生成第二階段的精細參數範圍"""
    # 基於第一階段最佳參數進行精細搜索
    parameters = {}
    
    for key, best_value in best_stage1_params.items():
        if 'percent' in key:
            # 百分比參數：在最佳值±20%範圍內精細搜索
            min_val = max(0.001, best_value * 0.8)
            max_val = best_value * 1.2
            step = (max_val - min_val) / 20  # 20個步長
            parameters[key] = np.arange(min_val, max_val + step, step)
        elif key == 'adx_threshold':
            # ADX閾值：在最佳值±5範圍內搜索
            min_val = max(10, best_value - 5)
            max_val = min(50, best_value + 5)
            parameters[key] = np.arange(min_val, max_val + 1, 1)
    
    return parameters

def main():
    """主函數：Dask分散式優化"""
    parser = argparse.ArgumentParser(description='Dask分散式參數優化')
    parser.add_argument('--scheduler', type=str, default='localhost:8786', 
                       help='Dask調度器地址 (預設: localhost:8786)')
    parser.add_argument('--stage1-results', type=str, default='../結果檔案/stage1_coarse_results.csv',
                       help='第一階段結果檔案路徑')
    
    args = parser.parse_args()
    
    print("=== Dask分散式參數優化器 ===")
    
    # 連接到Dask叢集
    try:
        client = Client(args.scheduler)
        print(f"已連接到Dask叢集: {args.scheduler}")
        print(f"叢集資訊: {client}")
    except Exception as e:
        print(f"無法連接到Dask叢集: {e}")
        print("將使用本地多進程模式...")
        client = Client(processes=True, n_workers=4, threads_per_worker=2)
    
    # 載入數據
    print("載入和處理數據...")
    df_processed = load_and_process_data()
    if df_processed.empty:
        return
    
    print(f"數據處理完成，共 {len(df_processed)} 筆記錄")
    
    # 按季度分割
    quarters = split_by_quarters(df_processed)
    print(f"數據分為 {len(quarters)} 個季度")
    
    # 讀取第一階段最佳參數
    try:
        stage1_df = pd.read_csv(args.stage1_results)
        best_stage1_params = stage1_df.iloc[0].to_dict()
        # 移除非參數欄位
        param_keys = ['adx_threshold', 'long_fixed_stop_loss_percent', 'long_trailing_activate_profit_percent',
                     'long_trailing_pullback_percent', 'long_trailing_min_profit_percent',
                     'short_fixed_stop_loss_percent', 'short_trailing_activate_profit_percent',
                     'short_trailing_pullback_percent', 'short_trailing_min_profit_percent']
        best_stage1_params = {k: v for k, v in best_stage1_params.items() if k in param_keys}
        print(f"第一階段最佳參數: {best_stage1_params}")
    except Exception as e:
        print(f"無法讀取第一階段結果: {e}")
        return
    
    # 生成第二階段參數範圍
    parameters = generate_stage2_parameters(best_stage1_params)
    
    # 生成參數組合
    keys = parameters.keys()
    all_combinations = list(itertools.product(*parameters.values()))
    print(f"第二階段總共 {len(all_combinations)} 個參數組合")
    
    # 創建Dask任務
    print("創建Dask分散式任務...")
    tasks = []
    for combo in all_combinations:
        params = dict(zip(keys, combo))
        task = test_parameter_combination_dask(params, quarters)
        tasks.append(task)
    
    # 執行分散式計算
    print("開始分散式優化...")
    start_time = time.time()
    
    # 使用Dask compute執行所有任務
    results = dask.compute(*tasks)
    results = [r for r in results if r is not None]
    
    end_time = time.time()
    print(f"優化完成！耗時: {end_time - start_time:.2f} 秒")
    print(f"成功測試 {len(results)} 個參數組合")
    
    if not results:
        print("沒有成功的參數組合")
        return
    
    # 排序並顯示結果
    results.sort(key=lambda x: x['stability_score'], reverse=True)
    
    print(f"\n=== 第二階段最佳參數 ===")
    best = results[0]
    print(f"穩定性評分: {best['stability_score']:.4f}")
    print(f"獲利一致性: {best['profit_consistency']:.2%}")
    print(f"平均季度獲利: {best['avg_profit']:.2f} USDT")
    print(f"平均勝率: {best['avg_win_rate']:.2f}%")
    print(f"總交易次數: {best['total_trades']} 次")
    
    print(f"\n最佳參數:")
    for key, value in best['params'].items():
        print(f"{key}: {value}")
    
    # 保存結果
    results_df = pd.DataFrame([{
        'rank': i+1,
        'stability_score': r['stability_score'],
        'profit_consistency': r['profit_consistency'],
        'avg_profit': r['avg_profit'],
        'avg_win_rate': r['avg_win_rate'],
        'total_trades': r['total_trades'],
        **r['params']
    } for i, r in enumerate(results)])
    
    results_df.to_csv('../結果檔案/stage2_fine_results.csv', index=False)
    print(f"\n結果已保存到 ../結果檔案/stage2_fine_results.csv")
    
    # 關閉客戶端
    client.close()

if __name__ == "__main__":
    main()
