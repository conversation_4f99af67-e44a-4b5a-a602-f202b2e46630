# Dask分散式運算教學 - ETH交易策略第二階段優化

## 概述

由於第二階段優化有550萬個參數組合，單機運算需要極長時間。使用Dask分散式運算可以將計算任務分散到多台電腦，大幅縮短運算時間。

## 系統架構

```
主控電腦 (Scheduler)
├── Dask Scheduler (調度器)
├── 數據存儲
└── 結果收集

工作電腦1 (Worker)
├── Dask Worker
└── CPU核心 (例如: 8核心)

工作電腦2 (Worker)
├── Dask Worker  
└── CPU核心 (例如: 16核心)

工作電腦3 (Worker)
├── Dask Worker
└── CPU核心 (例如: 12核心)
```

## 安裝需求

### 所有電腦都需要安裝：

```bash
# 安裝Python依賴
pip install dask[distributed] dask[complete]
pip install pandas numpy tqdm

# 或使用conda
conda install dask distributed pandas numpy tqdm
```

## 設置步驟

### 1. 主控電腦設置 (Scheduler)

主控電腦負責任務調度和數據管理。

```bash
# 啟動Dask調度器
dask-scheduler --host 0.0.0.0 --port 8786 --dashboard-address 0.0.0.0:8787
```

**重要參數說明：**
- `--host 0.0.0.0`: 允許其他電腦連接
- `--port 8786`: 調度器端口
- `--dashboard-address 0.0.0.0:8787`: 監控面板地址

**啟動後會顯示：**
```
distributed.scheduler - INFO - Scheduler at: tcp://*************:8786
distributed.scheduler - INFO - dashboard at: http://*************:8787/status
```

記下這個IP地址，其他電腦需要用到。

### 2. 工作電腦設置 (Workers)

每台工作電腦都需要啟動Worker連接到主控電腦。

```bash
# 連接到主控電腦的調度器
dask-worker tcp://*************:8786 --nthreads 8 --memory-limit 8GB

# 參數說明：
# tcp://*************:8786 - 主控電腦的調度器地址
# --nthreads 8 - 使用8個線程
# --memory-limit 8GB - 記憶體限制
```

**根據電腦配置調整參數：**
- 8核心電腦: `--nthreads 6 --memory-limit 6GB`
- 16核心電腦: `--nthreads 14 --memory-limit 12GB`
- 32核心電腦: `--nthreads 28 --memory-limit 24GB`

### 3. 檔案同步

確保所有電腦都有相同的檔案：

**主控電腦需要的檔案：**
```
copy-EMA_trader_byEricLiao/
├── 最佳化/程式檔案/dask_distributed_optimizer.py
├── lookingforthebest/
│   └── eth_usdt_4h_8years_binance.csv
└── 最佳化/結果檔案/stage1_coarse_results.csv
```

**工作電腦需要的檔案：**
```
copy-EMA_trader_byEricLiao/
├── 最佳化/程式檔案/dask_distributed_optimizer.py
└── lookingforthebest/
    └── eth_usdt_4h_8years_binance.csv
```

**同步方法：**
1. 使用網路共享資料夾
2. 使用rsync或scp複製檔案
3. 使用Git同步

## 執行優化

### 1. 檢查叢集狀態

在瀏覽器開啟監控面板：
```
http://*************:8787/status
```

確認所有Worker都已連接。

### 3. 監控執行進度

通過監控面板可以看到：
- 任務分配情況
- 各Worker的CPU使用率
- 記憶體使用情況
- 任務完成進度

## 效能估算

### 單機 vs 分散式對比

**單機運算 (20核心):**
- 550萬參數組合
- 每秒處理20個組合
- 預估時間: 550萬 ÷ 20 ÷ 3600 = 76小時

**4台電腦分散式 (總共80核心):**
- 550萬參數組合
- 每秒處理80個組合
- 預估時間: 550萬 ÷ 80 ÷ 3600 = 19小時

**8台電腦分散式 (總共160核心):**
- 550萬參數組合
- 每秒處理160個組合
- 預估時間: 550萬 ÷ 160 ÷ 3600 = 9.5小時

## 故障排除

### 常見問題

**1. Worker無法連接到Scheduler**
```bash
# 檢查防火牆設置
# Windows: 允許Python通過防火牆
# Linux: sudo ufw allow 8786

# 檢查網路連通性
ping *************
telnet ************* 8786
```

**2. 記憶體不足**
```bash
# 減少Worker的記憶體限制
dask-worker tcp://*************:8786 --nthreads 4 --memory-limit 4GB
```

**3. 檔案路徑錯誤**
確保所有電腦的檔案路徑一致，或使用絕對路徑。

### 效能調優

**1. 調整批次大小**
修改`dask_distributed_optimizer.py`中的批次處理：

```python
# 將任務分批處理，避免記憶體溢出
batch_size = 1000
for i in range(0, len(all_combinations), batch_size):
    batch = all_combinations[i:i+batch_size]
    # 處理批次
```

**2. 調整Worker數量**
```bash
# 每台電腦可以啟動多個Worker
dask-worker tcp://*************:8786 --nthreads 4 --memory-limit 4GB &
dask-worker tcp://*************:8786 --nthreads 4 --memory-limit 4GB &
```

## 結果分析

執行完成後會產生：
- `stage2_fine_results.csv`: 詳細結果
- 監控面板的執行統計

**最佳參數將顯示：**
```
=== 第二階段最佳參數 ===
穩定性評分: 0.8234
獲利一致性: 78.12%
平均季度獲利: 2456.78 USDT
平均勝率: 65.43%
總交易次數: 1247 次

最佳參數:
adx_threshold: 26
long_fixed_stop_loss_percent: 0.0234
long_trailing_activate_profit_percent: 0.0156
...
```

## 安全注意事項

1. **網路安全**: 確保在可信任的網路環境中運行
2. **資料備份**: 定期備份重要結果檔案
3. **資源監控**: 避免過度使用電腦資源影響其他工作

## 進階配置

### 使用配置檔案

創建`dask.yaml`配置檔案：

```yaml
distributed:
  scheduler:
    allowed-failures: 3
    bandwidth: 100MB/s
  worker:
    memory:
      target: 0.6
      spill: 0.7
      pause: 0.8
      terminate: 0.95
```

### 自動重啟機制

```bash
# 使用systemd或supervisor自動重啟Worker
# 創建服務檔案確保Worker持續運行
```

這樣設置後，您就可以利用多台電腦的計算能力，大幅加速第二階段的參數優化過程！

### 2. 執行分散式優化

在主控電腦的最佳化資料夾執行：

```bash
cd 最佳化/程式檔案

# 基本執行
python dask_distributed_optimizer.py

# 指定調度器地址
python dask_distributed_optimizer.py --scheduler tcp://*************:8786

# 指定第一階段結果檔案
python dask_distributed_optimizer.py --stage1-results ../結果檔案/stage1_coarse_results.csv
```

### 3. 監控執行進度

通過監控面板可以看到：
- 任務分配情況
- 各Worker的CPU使用率
- 記憶體使用情況
- 任務完成進度

## 效能估算

### 單機 vs 分散式對比

**單機運算 (20核心):**
- 550萬參數組合
- 每秒處理20個組合
- 預估時間: 550萬 ÷ 20 ÷ 3600 = 76小時

**4台電腦分散式 (總共80核心):**
- 550萬參數組合
- 每秒處理80個組合  
- 預估時間: 550萬 ÷ 80 ÷ 3600 = 19小時

**8台電腦分散式 (總共160核心):**
- 550萬參數組合
- 每秒處理160個組合
- 預估時間: 550萬 ÷ 160 ÷ 3600 = 9.5小時

## 故障排除

### 常見問題

**1. Worker無法連接到Scheduler**
```bash
# 檢查防火牆設置
# Windows: 允許Python通過防火牆
# Linux: sudo ufw allow 8786

# 檢查網路連通性
ping *************
telnet ************* 8786
```

**2. 記憶體不足**
```bash
# 減少Worker的記憶體限制
dask-worker tcp://*************:8786 --nthreads 4 --memory-limit 4GB
```

**3. 檔案路徑錯誤**
確保所有電腦的檔案路徑一致，或使用絕對路徑。

### 效能調優

**1. 調整批次大小**
修改`dask_distributed_optimizer.py`中的批次處理：

```python
# 將任務分批處理，避免記憶體溢出
batch_size = 1000
for i in range(0, len(all_combinations), batch_size):
    batch = all_combinations[i:i+batch_size]
    # 處理批次
```

**2. 調整Worker數量**
```bash
# 每台電腦可以啟動多個Worker
dask-worker tcp://*************:8786 --nthreads 4 --memory-limit 4GB &
dask-worker tcp://*************:8786 --nthreads 4 --memory-limit 4GB &
```

## 結果分析

執行完成後會產生：
- `stage2_fine_results.csv`: 詳細結果
- 監控面板的執行統計

**最佳參數將顯示：**
```
=== 第二階段最佳參數 ===
穩定性評分: 0.8234
獲利一致性: 78.12%
平均季度獲利: 2456.78 USDT
平均勝率: 65.43%
總交易次數: 1247 次

最佳參數:
adx_threshold: 26
long_fixed_stop_loss_percent: 0.0234
long_trailing_activate_profit_percent: 0.0156
...
```

## 安全注意事項

1. **網路安全**: 確保在可信任的網路環境中運行
2. **資料備份**: 定期備份重要結果檔案
3. **資源監控**: 避免過度使用電腦資源影響其他工作

## 進階配置

### 使用配置檔案

創建`dask.yaml`配置檔案：

```yaml
distributed:
  scheduler:
    allowed-failures: 3
    bandwidth: 100MB/s
  worker:
    memory:
      target: 0.6
      spill: 0.7
      pause: 0.8
      terminate: 0.95
```

### 自動重啟機制

```bash
# 使用systemd或supervisor自動重啟Worker
# 創建服務檔案確保Worker持續運行
```

這樣設置後，您就可以利用多台電腦的計算能力，大幅加速第二階段的參數優化過程！
