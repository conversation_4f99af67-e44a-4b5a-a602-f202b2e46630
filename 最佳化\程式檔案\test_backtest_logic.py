#!/usr/bin/env python3
"""
測試回測邏輯的簡單腳本
"""
import pandas as pd
import numpy as np

def simple_backtest_test():
    """簡單的回測邏輯測試"""
    # 創建測試數據
    test_data = {
        'timestamp': pd.date_range('2024-01-01', periods=10, freq='4H'),
        'open': [2000, 2010, 2020, 2030, 2040, 2050, 2060, 2070, 2080, 2090],
        'high': [2005, 2015, 2025, 2035, 2045, 2055, 2065, 2075, 2085, 2095],
        'low': [1995, 2005, 2015, 2025, 2035, 2045, 2055, 2065, 2075, 2085],
        'close': [2000, 2010, 2020, 2030, 2040, 2050, 2060, 2070, 2080, 2090],
        'volume': [1000] * 10,
        'ema90': [1990, 2000, 2010, 2020, 2030, 2040, 2050, 2060, 2070, 2080],
        'ema200': [1980, 1990, 2000, 2010, 2020, 2030, 2040, 2050, 2060, 2070],
        'adx': [20, 25, 30, 25, 20, 25, 30, 25, 20, 25]
    }
    
    df = pd.DataFrame(test_data)
    df.set_index('timestamp', inplace=True)
    
    # 測試參數
    params = {
        'adx_threshold': 20,
        'long_fixed_stop_loss_percent': 0.02,  # 2%
        'long_trailing_activate_profit_percent': 0.03,  # 3%
        'long_trailing_pullback_percent': 0.02,  # 2%
        'long_trailing_min_profit_percent': 0.01,  # 1%
        'short_fixed_stop_loss_percent': 0.02,
        'short_trailing_activate_profit_percent': 0.03,
        'short_trailing_pullback_percent': 0.02,
        'short_trailing_min_profit_percent': 0.01
    }
    
    # 回測邏輯
    initial_capital = 10000
    capital = 10000
    position_type = None
    position_size = 0
    entry_price = 0
    trade_log = []
    
    print(f"初始資金: {initial_capital} USDT")
    print("=" * 50)
    
    for i, (timestamp, row) in enumerate(df.iterrows()):
        current_close = row['close']
        current_high = row['high']
        current_low = row['low']
        current_adx = row['adx']
        
        print(f"第{i+1}根K線 - 時間: {timestamp}, 價格: {current_close}, ADX: {current_adx}")
        
        if position_type is None:
            # 開倉邏輯
            if (row['ema90'] > row['ema200'] and current_adx > params['adx_threshold'] and 
                current_close > row['ema90']):
                # 開多單
                position_size = capital * 0.1 / current_close
                entry_price = current_close
                position_type = 'LONG'
                trade_log.append({'type': 'LONG_ENTRY', 'price': current_close, 'size': position_size})
                print(f"  -> 開多單: 價格={current_close}, 倉位={position_size:.6f} ETH, 使用資金={position_size * current_close:.2f} USDT")
                
        elif position_type == 'LONG':
            # 多單管理 - 簡化版，只檢查固定停損
            long_fixed_stop_loss_price = entry_price * (1 - params['long_fixed_stop_loss_percent'])
            long_stop_loss = current_close <= long_fixed_stop_loss_price
            
            if long_stop_loss:
                # 平多單
                exit_value = position_size * current_close
                entry_value = position_size * entry_price
                profit_loss = exit_value - entry_value
                capital = capital - entry_value + exit_value
                trade_log.append({'type': 'LONG_EXIT', 'price': current_close, 'profit_loss': profit_loss})
                print(f"  -> 平多單: 價格={current_close}, 獲利={profit_loss:.2f} USDT, 新資金={capital:.2f} USDT")
                
                # 重置倉位
                position_type = None
                position_size = 0
                entry_price = 0
        
        print(f"  當前資金: {capital:.2f} USDT")
        print()
    
    final_profit = capital - initial_capital
    print("=" * 50)
    print(f"最終資金: {capital:.2f} USDT")
    print(f"總獲利: {final_profit:.2f} USDT")
    print(f"獲利率: {(final_profit/initial_capital)*100:.2f}%")
    print(f"交易記錄: {len(trade_log)} 筆")
    
    for trade in trade_log:
        print(f"  {trade}")

if __name__ == "__main__":
    simple_backtest_test()
