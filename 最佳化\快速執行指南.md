# 🚀 快速執行指南

## 📂 檔案結構
```
最佳化/
├── requirements.txt          # 套件需求檔案
├── 安裝說明.md              # 詳細安裝說明
├── 快速執行指南.md          # 本檔案
├── 程式檔案/                # 主要程式檔案
│   ├── fast_stability_optimizer.py          # CPU版本
│   └── fast_stability_optimizer-GPUversion.py # GPU版本
└── 結果檔案/                # 優化結果檔案
    ├── stage1_coarse_results.csv
    ├── stage2_fine_results.csv
    └── two_stage_optimization_results.csv
```

## ⚡ 一鍵執行

### 1. 安裝套件
```bash
# 在最佳化資料夾中
pip install -r requirements.txt
```

### 2. 執行優化
```bash
# 進入程式檔案資料夾
cd 程式檔案

# 方案A：推薦混合模式（CPU第一階段 + GPU第二階段）
python fast_stability_optimizer.py --stage1
python fast_stability_optimizer-GPUversion.py --stage2

# 方案B：純CPU模式（如果沒有GPU）
python fast_stability_optimizer.py

# 方案C：純GPU模式（如果有GPU且想要最快速度）
python fast_stability_optimizer-GPUversion.py

# 方案D：獨立第二階段大範圍搜索（GPU超快速全域搜索）
python fast_stability_optimizer-GPUversion.py --independent
```

## 🔍 狀態檢查

### 檢查GPU是否可用
```bash
cd 程式檔案
python fast_stability_optimizer-GPUversion.py --gpu-info
```

### 檢查優化進度
```bash
cd 程式檔案
python fast_stability_optimizer.py --status
python fast_stability_optimizer-GPUversion.py --status
```

## ⏱️ 執行時間預估

| 模式 | 第一階段 | 第二階段 | 總計 |
|------|----------|----------|------|
| 純CPU | 30分鐘 | 30分鐘 | 60分鐘 |
| 純GPU | 10分鐘 | 6分鐘 | 16分鐘 |
| **推薦混合** | **30分鐘(CPU)** | **6分鐘(GPU)** | **36分鐘** |
| **獨立大範圍搜索** | **-** | **3-8分鐘(GPU)** | **3-8分鐘** |

## 📊 結果檔案說明

執行完成後，在 `結果檔案/` 中會生成：

- **`stage1_coarse_results.csv`** - 第一階段粗略搜索結果
- **`stage2_fine_results.csv`** - 第二階段細化搜索結果  
- **`two_stage_optimization_results.csv`** - 最終合併結果（★重要）
- **`independent_stage2_results.csv`** - 獨立第二階段結果
- **`independent_optimization_results.csv`** - 獨立模式最終結果（★大範圍搜索）

## 🎯 最佳參數查看

```python
import pandas as pd

# 讀取最終結果（傳統兩階段）
df = pd.read_csv('../結果檔案/two_stage_optimization_results.csv')

# 讀取獨立大範圍搜索結果
df_independent = pd.read_csv('../結果檔案/independent_optimization_results.csv')

# 查看最佳參數
best = df.iloc[0]
best_independent = df_independent.iloc[0]

print("=== 傳統兩階段最佳結果 ===")
print(f"穩定性分數: {best['stability_score']:.2f}")
print(f"獲利一致性: {best['profit_consistency']:.1%}")

print("\n=== 獨立大範圍搜索最佳結果 ===")
print(f"穩定性分數: {best_independent['stability_score']:.2f}")
print(f"獲利一致性: {best_independent['profit_consistency']:.1%}")
print(f"ADX閾值: {best_independent['adx_threshold']}")
print(f"多頭停損: {best_independent['long_fixed_stop_loss_percent']:.3f}")
```

## 🚨 常見錯誤解決

### 找不到檔案
```bash
# 確保在正確的資料夾中
cd "D:\Users\User\Desktop\copy-EMA_trader_byEricLiao\最佳化\程式檔案"
```

### GPU不可用
- 檢查NVIDIA驅動是否安裝
- 檢查CUDA版本是否匹配
- 可以跳過GPU，使用CPU模式

### 記憶體不足
- 關閉其他程式釋放記憶體
- GPU版本會自動管理記憶體
- 程式支援中斷恢復，可分批執行

## 💡 使用技巧

1. **先跑第一階段看效果**：`python fast_stability_optimizer.py --stage1`
2. **滿意再跑第二階段**：`python fast_stability_optimizer-GPUversion.py --stage2`
3. **可隨時中斷**：程式會自動保存進度
4. **混合模式最實用**：兼顧穩定性和速度
5. **🚀 GPU超快模式**：`python fast_stability_optimizer-GPUversion.py --independent`
6. **大範圍搜索**：獨立模式不受第一階段限制，探索更廣參數空間