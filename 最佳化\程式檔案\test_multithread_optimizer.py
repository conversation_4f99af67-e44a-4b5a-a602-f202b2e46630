#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試多線程優化器的簡單腳本
"""

import sys
import os

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """測試是否能正確導入修改後的模組"""
    try:
        # 嘗試導入修改後的模組
        import dask_distributed_optimizer as optimizer
        print("✓ 成功導入 dask_distributed_optimizer 模組")
        
        # 檢查主要函數是否存在
        functions_to_check = [
            'load_and_process_data',
            'split_by_quarters', 
            'run_backtest_single_quarter',
            'test_parameter_combination_multithread',
            'generate_stage2_parameters',
            'main'
        ]
        
        for func_name in functions_to_check:
            if hasattr(optimizer, func_name):
                print(f"✓ 函數 {func_name} 存在")
            else:
                print(f"✗ 函數 {func_name} 不存在")
                
        return True
        
    except ImportError as e:
        print(f"✗ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他錯誤: {e}")
        return False

def test_data_loading():
    """測試數據載入功能"""
    try:
        import dask_distributed_optimizer as optimizer
        print("\n測試數據載入...")
        
        # 檢查數據檔案是否存在
        data_path = '../../下載歷史K棒/lookingforthebest/eth_usdt_4h_8years_binance.csv'
        if os.path.exists(data_path):
            print(f"✓ 數據檔案存在: {data_path}")
            
            # 嘗試載入數據
            df = optimizer.load_and_process_data()
            if not df.empty:
                print(f"✓ 數據載入成功，共 {len(df)} 筆記錄")
                print(f"✓ 數據欄位: {list(df.columns)}")
                return True
            else:
                print("✗ 數據載入失敗，返回空的 DataFrame")
                return False
        else:
            print(f"✗ 數據檔案不存在: {data_path}")
            return False
            
    except Exception as e:
        print(f"✗ 數據載入測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("=== 多線程優化器測試 ===\n")
    
    # 測試導入
    import_success = test_import()
    
    if import_success:
        # 測試數據載入
        data_success = test_data_loading()
        
        if data_success:
            print("\n✓ 所有基本測試通過！")
            print("\n使用方法:")
            print("cd 最佳化/程式檔案")
            print("python dask_distributed_optimizer.py --threads 8")
            print("\n參數說明:")
            print("--threads: 指定使用的執行緒數量 (預設: 8)")
            print("--stage1-results: 第一階段結果檔案路徑")
        else:
            print("\n✗ 數據載入測試失敗")
    else:
        print("\n✗ 模組導入測試失敗")

if __name__ == "__main__":
    main()
