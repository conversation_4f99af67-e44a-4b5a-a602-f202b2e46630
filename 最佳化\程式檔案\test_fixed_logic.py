#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試修正後的回測邏輯
"""

def test_trading_logic():
    print("=== 測試修正後的交易邏輯 ===")
    
    # 初始資金
    initial_capital = 10000
    capital = initial_capital
    
    # 測試多單
    print("\n--- 測試多單 ---")
    print(f"初始資金: ${capital}")
    
    # 開多單 - 用50%資金
    entry_price = 2500
    invest_amount = capital * 0.5  # 50%資金
    position_size = invest_amount / entry_price
    capital -= invest_amount
    
    print(f"開多單:")
    print(f"  入場價: ${entry_price}")
    print(f"  投入金額: ${invest_amount}")
    print(f"  倉位大小: {position_size:.6f} ETH")
    print(f"  剩餘資金: ${capital}")
    
    # 平多單 - 獲利
    exit_price = 2600
    exit_value = position_size * exit_price
    profit_loss = exit_value - (position_size * entry_price)
    capital += exit_value
    
    print(f"平多單 (獲利):")
    print(f"  出場價: ${exit_price}")
    print(f"  賣出收入: ${exit_value:.2f}")
    print(f"  獲利: ${profit_loss:.2f}")
    print(f"  總資金: ${capital:.2f}")
    print(f"  總獲利率: {((capital - initial_capital) / initial_capital * 100):.2f}%")
    
    # 重置
    capital = initial_capital
    
    # 測試空單
    print("\n--- 測試空單 ---")
    print(f"初始資金: ${capital}")
    
    # 開空單 - 用50%資金
    entry_price = 2500
    invest_amount = capital * 0.5  # 50%資金
    position_size = invest_amount / entry_price
    capital -= invest_amount
    
    print(f"開空單:")
    print(f"  入場價: ${entry_price}")
    print(f"  投入金額: ${invest_amount}")
    print(f"  倉位大小: {position_size:.6f} ETH")
    print(f"  剩餘資金: ${capital}")
    
    # 平空單 - 獲利
    exit_price = 2400
    profit_loss = (entry_price - exit_price) * position_size
    capital += (position_size * entry_price) + profit_loss
    
    print(f"平空單 (獲利):")
    print(f"  出場價: ${exit_price}")
    print(f"  獲利: ${profit_loss:.2f}")
    print(f"  總資金: ${capital:.2f}")
    print(f"  總獲利率: {((capital - initial_capital) / initial_capital * 100):.2f}%")
    
    # 測試虧損情況
    print("\n--- 測試虧損情況 ---")
    capital = initial_capital
    
    # 開多單然後虧損
    entry_price = 2500
    invest_amount = capital * 0.5
    position_size = invest_amount / entry_price
    capital -= invest_amount
    
    # 平多單 - 虧損
    exit_price = 2400
    exit_value = position_size * exit_price
    profit_loss = exit_value - (position_size * entry_price)
    capital += exit_value
    
    print(f"多單虧損:")
    print(f"  入場價: ${entry_price}, 出場價: ${exit_price}")
    print(f"  虧損: ${profit_loss:.2f}")
    print(f"  總資金: ${capital:.2f}")
    print(f"  總獲利率: {((capital - initial_capital) / initial_capital * 100):.2f}%")

if __name__ == "__main__":
    test_trading_logic()
