"""
🏆 ETH 4小時自動交易策略 - 最佳參數版本
📅 更新日期: 2025-01-12
🎯 參數來源: 2020-2025年優化結果 (穩定性評分48.38, 獲利5371.25 USDT)
✅ 驗證結果: 2017-2019年回測獲利1262.19 USDT (年化42.1%)
📊 策略特點: 95.45%季度獲利率, 54.74%平均勝率, 14.06%最大回撤
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import itertools
import multiprocessing
import os
import time
import ccxt
from dotenv import load_dotenv
import json

# 載入 .env 檔案中的環境變數
load_dotenv()

# --- 全局配置 ---
BYBIT_API_KEY = os.getenv("BYBIT_API_KEY")
BYBIT_API_SECRET = os.getenv("BYBIT_API_SECRET")
SYMBOL = 'ETH/USDT' # 交易對
TIMEFRAME = '4h'    # K線時間週期，與您的策略一致
# 注意: 實際交易時，起始資金和數量百分比會基於您的真實帳戶餘額
# 但我們保留這些參數在 TradingStrategy 中，方便傳遞和調整
DEFAULT_QTY_PERCENT = 70 # 每次交易使用帳戶可用餘額的百分比
TRADE_SLEEP_SECONDS = 60 # 每隔多久檢查一次新K線 (60秒檢查一次)
FETCH_KLINE_LIMIT = 300 # 獲取多少根K線用於指標計算 (確保涵蓋EMA200所需的數據量)

# 定義保存狀態的檔案路徑
STATE_FILE = 'strategy_state.json'

# --- 1. 數據載入 (從 Bybit API 獲取數據) ---
def fetch_bybit_klines(symbol, timeframe, limit=FETCH_KLINE_LIMIT):
    """
    從 Bybit 獲取指定交易對和時間週期的 K 線數據。
    """
    exchange = ccxt.bybit({
        'apiKey': BYBIT_API_KEY,
        'secret': BYBIT_API_SECRET,
        'sandbox': False,  # 實際交易模式
        'options': {
            'defaultType': 'future', # 或者 'spot', 'margin' 等，根據您的交易類型設定
            'adjustForTimeDifference': True, # 自動調整時間差
            'recvWindow': 120000  # 增加接收窗口時間到2分鐘
        }
    })

    # 同步時間
    try:
        exchange.load_time_difference()
    except:
        pass
    
    # 載入市場資訊，ccxt 需要知道市場資訊才能正確處理交易對
    exchange.load_markets() 

    try:
        # 獲取 K 線數據
        ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        df.columns = [col.lower() for col in df.columns] # 統一列名為小寫
        return df
    except ccxt.NetworkError as e:
        print(f"網路錯誤: {e}")
        return pd.DataFrame()
    except ccxt.ExchangeError as e:
        print(f"交易所錯誤: {e}")
        return pd.DataFrame()
    except Exception as e:
        print(f"獲取 K 線數據時發生未知錯誤: {e}")
        return pd.DataFrame()

# --- 2. 指標計算 ---
def calculate_ema(series, period):
    """計算指數移動平均線"""
    return series.ewm(span=period, adjust=False).mean()

def calculate_adx(high, low, close, period=14):
    """計算ADX指標"""
    # 計算True Range
    tr1 = high - low
    tr2 = abs(high - close.shift(1))
    tr3 = abs(low - close.shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

    # 計算方向移動
    plus_dm = high.diff()
    minus_dm = -low.diff()

    plus_dm[plus_dm < 0] = 0
    minus_dm[minus_dm < 0] = 0

    # 當+DM > -DM時，-DM = 0；當-DM > +DM時，+DM = 0
    plus_dm[(plus_dm <= minus_dm)] = 0
    minus_dm[(minus_dm <= plus_dm)] = 0

    # 計算平滑的TR和DM
    atr = tr.rolling(window=period).mean()
    plus_di = 100 * (plus_dm.rolling(window=period).mean() / atr)
    minus_di = 100 * (minus_dm.rolling(window=period).mean() / atr)

    # 計算DX和ADX
    dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
    adx = dx.rolling(window=period).mean()

    return adx, plus_di, minus_di

def calculate_rsi(close, period=14):
    """計算RSI指標"""
    delta = close.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd(close, fast=12, slow=26, signal=9):
    """計算MACD指標"""
    ema_fast = calculate_ema(close, fast)
    ema_slow = calculate_ema(close, slow)
    macd_line = ema_fast - ema_slow
    signal_line = calculate_ema(macd_line, signal)
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram

def calculate_indicators(df):
    """計算所有技術指標"""
    df = df.copy()

    # EMA指標
    df['ema90'] = calculate_ema(df['close'], 90)
    df['ema200'] = calculate_ema(df['close'], 200)

    # ADX指標
    adx, plus_di, minus_di = calculate_adx(df['high'], df['low'], df['close'], 14)
    df['adx'] = adx
    df['plus_di'] = plus_di
    df['minus_di'] = minus_di

    # RSI指標
    df['rsi'] = calculate_rsi(df['close'], 14)

    # MACD指標
    macd_line, signal_line, histogram = calculate_macd(df['close'])
    df['macd'] = macd_line
    df['macd_signal'] = signal_line
    df['macd_histogram'] = histogram

    return df.dropna()

# --- 3. 交易邏輯實現 ---
class TradingStrategy:
    def __init__(self, initial_capital_dummy=1000, default_qty_percent=70, # initial_capital_dummy 僅作回測兼容
                 # 🏆 最佳參數組合 (2020-2025年優化結果，穩定性評分48.38)
                 adx_threshold=29,  # 原22 → 29 (更嚴格的趨勢判斷)
                 long_fixed_stop_loss_percent=0.019,  # 原0.015 → 0.019 (1.9%)
                 long_trailing_activate_profit_percent=0.011,  # 原0.015 → 0.011 (1.1%)
                 long_trailing_pullback_percent=0.054,  # 原0.06 → 0.054 (5.4%)
                 long_trailing_min_profit_percent=0.024,  # 原0.025 → 0.024 (2.4%)
                 short_fixed_stop_loss_percent=0.013,  # 原0.015 → 0.013 (1.3%)
                 short_trailing_activate_profit_percent=0.019,  # 原0.015 → 0.019 (1.9%)
                 short_trailing_pullback_percent=0.018,  # 原0.06 → 0.018 (1.8%)
                 short_trailing_min_profit_percent=0.016):  # 原0.02 → 0.016 (1.6%)
        
        self.default_qty_percent = default_qty_percent
        
        # ccxt 交易所實例 - 統一帳戶合約交易
        self.exchange = ccxt.bybit({
            'apiKey': BYBIT_API_KEY,
            'secret': BYBIT_API_SECRET,
            'sandbox': False,  # 實際交易模式
            'options': {
                'defaultType': 'linear', # 統一帳戶線性合約
                'adjustForTimeDifference': True,
                'recvWindow': 120000,  # 增加接收窗口時間到2分鐘
                'unified': True  # 啟用統一帳戶模式
            },
            'enableRateLimit': True # 啟用速率限制，避免被交易所 ban IP
        })

        # 同步時間
        try:
            self.exchange.load_time_difference()
        except:
            pass
        self.exchange.load_markets()
        self.symbol = SYMBOL

        # 提醒用戶確認槓桿設置
        print("⚠️ 重要提醒: 請確認在Bybit平台手動設置ETH/USDT槓桿為1倍")
        print("   1. 登入Bybit網站 -> 合約交易")
        print("   2. 選擇ETH/USDT交易對")
        print("   3. 將槓桿設置為1x")
        print("   4. 確認設置後再開始交易")

        # 嘗試從檔案加載狀態
        if not self.load_state():
            print("未找到或無法加載狀態檔案，初始化策略狀態...")
            self.current_capital = self._get_free_balance() # 從 Bybit 獲取當前可用資金
            self.position_size = self._get_current_position_size() # 從 Bybit 獲取當前持倉

            self.entry_price = 0  # 添加缺失的屬性
            self.long_entry_price = None
            self.long_peak = None
            self.long_trail_stop_price = None
            self.is_long_trail_active = False

            self.short_entry_price = None
            self.short_trough = None
            self.short_trail_stop_price = None
            self.is_short_trail_active = False

            self.peak_capital = self.current_capital
            self.max_drawdown = 0.0
        else:
            print("策略狀態已從檔案加載。")
            # 加載後仍需從 Bybit 更新實際資金和倉位，以防手動操作或外部變化
            self.current_capital = self._get_free_balance()
            self.position_size = self._get_current_position_size()
            print(f"從檔案加載後，實際查詢帳戶：當前可用資金: {self.current_capital:.2f} USDT, 當前持倉量: {self.position_size:.3f} {SYMBOL.split('/')[0]}")


        # 策略參數 (這些參數在實時運行中是固定的，不需要從狀態檔案加載)
        self.adx_threshold = adx_threshold  
        self.long_fixed_stop_loss_percent = long_fixed_stop_loss_percent
        self.long_trailing_activate_profit_percent = long_trailing_activate_profit_percent
        self.long_trailing_pullback_percent = long_trailing_pullback_percent
        self.long_trailing_min_profit_percent = long_trailing_min_profit_percent
        self.short_fixed_stop_loss_percent = short_fixed_stop_loss_percent
        self.short_trailing_activate_profit_percent = short_trailing_activate_profit_percent
        self.short_trailing_pullback_percent = short_trailing_pullback_percent
        self.short_trailing_min_profit_percent = short_trailing_min_profit_percent

        self.trade_log = [] # 實時交易時，日誌記錄更為關鍵
        self.start_date = datetime(2024, 1, 1, 0, 0) # 回測用

        print(f"策略初始化完成。當前可用資金: {self.current_capital:.2f} USDT, 當前持倉量: {self.position_size:.3f} {SYMBOL.split('/')[0]}")

    def _get_free_balance(self, currency='USDT'):
        """獲取 Bybit 帳戶的可用資金"""
        try:
            balance = self.exchange.fetch_balance()
            return balance['free'][currency]
        except Exception as e:
            print(f"獲取帳戶餘額失敗: {e}")
            return 0

    def _get_current_position_size(self):
        """獲取 Bybit 統一帳戶當前指定交易對的持倉量，並同步進場價格"""
        try:
            # 使用原始API直接獲取持倉（這個方法有效）
            if hasattr(self.exchange, 'private_get_v5_position_list'):
                params = {'category': 'linear', 'symbol': 'ETHUSDT'}
                response = self.exchange.private_get_v5_position_list(params)

                if 'result' in response and 'list' in response['result']:
                    positions = response['result']['list']
                    for pos in positions:
                        size = float(pos.get('size', 0))
                        if size > 0:
                            side = pos.get('side', '')
                            avg_price = float(pos.get('avgPrice', 0)) if pos.get('avgPrice') != 'N/A' else 0
                            mark_price = pos.get('markPrice', 'N/A')
                            unrealized_pnl = pos.get('unrealisedPnl', 'N/A')

                            side_text = "多單" if side == 'Buy' else "空單"
                            print(f"📊 檢測到持倉: {side_text} {size} ETH")
                            print(f"   開倉價格: ${avg_price}")
                            print(f"   標記價格: ${mark_price}")
                            print(f"   未實現盈虧: ${unrealized_pnl}")

                            # 🔧 修正：同步進場價格到策略狀態
                            if side == 'Buy' and avg_price > 0:
                                # 如果檢測到多單但策略狀態中沒有進場價格，則同步
                                if self.long_entry_price is None or self.long_entry_price == 0:
                                    self.long_entry_price = avg_price
                                    self.entry_price = avg_price
                                    print(f"🔧 同步多單進場價格: ${avg_price}")

                                    # 🔧 修正移動停損初始化：嘗試恢復合理的移動停損狀態
                                    current_price = float(mark_price) if mark_price != 'N/A' else avg_price
                                    profit_percent = (current_price - avg_price) / avg_price

                                    # 如果當前已有利潤且超過激活閾值，應該激活移動停損
                                    if profit_percent > self.long_trailing_activate_profit_percent:
                                        self.long_peak = current_price  # 設定當前價格為峰值
                                        self.long_trail_stop_price = avg_price * (1 + self.long_trailing_min_profit_percent)
                                        self.is_long_trail_active = True
                                        print(f"🔧 恢復移動停損狀態: 峰值${self.long_peak:.2f}, 止損價${self.long_trail_stop_price:.2f}")
                                    else:
                                        # 如果沒有足夠利潤，重置移動停損狀態
                                        self.long_peak = None
                                        self.long_trail_stop_price = None
                                        self.is_long_trail_active = False
                                        print(f"🔧 重置移動停損狀態（當前無足夠利潤激活）")

                                    self.save_state()
                                return size
                            elif side == 'Sell' and avg_price > 0:
                                # 如果檢測到空單但策略狀態中沒有進場價格，則同步
                                if self.short_entry_price is None or self.short_entry_price == 0:
                                    self.short_entry_price = avg_price
                                    self.entry_price = avg_price
                                    print(f"🔧 同步空單進場價格: ${avg_price}")

                                    # 🔧 修正移動停損初始化：嘗試恢復合理的移動停損狀態
                                    current_price = float(mark_price) if mark_price != 'N/A' else avg_price
                                    profit_percent = (avg_price - current_price) / avg_price

                                    # 如果當前已有利潤且超過激活閾值，應該激活移動停損
                                    if profit_percent > self.short_trailing_activate_profit_percent:
                                        self.short_trough = current_price  # 設定當前價格為谷值
                                        self.short_trail_stop_price = avg_price * (1 - self.short_trailing_min_profit_percent)
                                        self.is_short_trail_active = True
                                        print(f"🔧 恢復移動停損狀態: 谷值${self.short_trough:.2f}, 止損價${self.short_trail_stop_price:.2f}")
                                    else:
                                        # 如果沒有足夠利潤，重置移動停損狀態
                                        self.short_trough = None
                                        self.short_trail_stop_price = None
                                        self.is_short_trail_active = False
                                        print(f"🔧 重置移動停損狀態（當前無足夠利潤激活）")

                                    self.save_state()
                                return -size

            print("📊 無持倉")
            return 0

        except Exception as e:
            print(f"獲取持倉失敗: {e}")
            return 0

    def _place_order(self, side, trade_qty, price_type='market'):
        """下單到 Bybit 統一帳戶"""
        try:
            # 確保數量是非零的
            if trade_qty <= 0:
                print(f"嘗試下單數量為 {trade_qty}，訂單取消。")
                return None

            # 嘗試不同的下單參數組合
            print(f"🔄 嘗試下單: {side} {trade_qty} {self.symbol}")

            # 方法1: 強制使用線性合約參數
            try:
                params = {'category': 'linear'}  # 強制使用合約交易
                order = self.exchange.create_order(
                    symbol=self.symbol,
                    type=price_type,
                    side=side,
                    amount=trade_qty,
                    price=None,
                    params=params
                )
            except Exception as e1:
                print(f"方法1失敗: {e1}")

                # 方法2: 使用原始API確保合約交易
                try:
                    if hasattr(self.exchange, 'private_post_v5_order_create'):
                        order_params = {
                            'category': 'linear',  # 強制線性合約
                            'symbol': 'ETHUSDT',
                            'side': side.capitalize(),
                            'orderType': 'Market',
                            'qty': str(trade_qty)
                        }
                        response = self.exchange.private_post_v5_order_create(order_params)
                        if response.get('retCode') == 0:
                            order = {
                                'id': response['result']['orderId'],
                                'side': side,
                                'amount': trade_qty,
                                'symbol': self.symbol,
                                'type': price_type,
                                'status': 'closed'
                            }
                        else:
                            raise Exception(f"API錯誤: {response}")
                    else:
                        raise Exception("無可用的下單方法")
                except Exception as e2:
                    print(f"方法2失敗: {e2}")
                    raise e2
            print(f"下單成功: {order['side']} {order['amount']} {order['symbol']} @ {order.get('price', 'N/A')} (類型: {order['type']})")
            self.trade_log.append({
                'time': datetime.now().isoformat(), 'type': f"ORDER_{side.upper()}", # isoformat 讓 datetime 可 JSON 序列化
                'price': order.get('price', 'N/A'), 'qty': trade_qty,
                'status': 'PLACED', 'order_id': order['id']
            })
            return order
        except ccxt.InsufficientFunds as e:
            print(f"資金不足，無法下單 {side} {trade_qty} {self.symbol}")
            print(f"詳細錯誤: {e}")
            self.trade_log.append({'time': datetime.now().isoformat(), 'type': 'ERROR_INSUFFICIENT_FUNDS', 'details': str(e)})
        except ccxt.InvalidOrder as e:
            print(f"無效訂單: {e}")
            self.trade_log.append({'time': datetime.now().isoformat(), 'type': 'ERROR_INVALID_ORDER', 'details': str(e)})
        except Exception as e:
            print(f"下單失敗: {e}")
            self.trade_log.append({'time': datetime.now().isoformat(), 'type': 'ERROR_ORDER_FAILED', 'details': str(e)})
        except Exception as e:
            print(f"下單失敗: {e}")
            self.trade_log.append({'time': datetime.now().isoformat(), 'type': 'ERROR_ORDER_FAILED', 'details': str(e)})
        return None

    def _close_position(self, current_close):
        """平倉當前持有的所有倉位"""
        print(f"\n🔄 開始平倉程序...")
        
        # 添加重試機制確保狀態同步
        max_retries = 3
        actual_position = 0
        
        for attempt in range(max_retries):
            print(f"📊 嘗試 {attempt+1}/{max_retries}: 查詢當前持倉...")
            actual_position = self._get_current_position_size()
            
            if actual_position == 0:
                if attempt < max_retries - 1:
                    print(f"⏳ 查詢顯示無持倉，等待2秒後重試...")
                    time.sleep(2)
                    continue
                else:
                    print("📊 多次查詢確認無實際持倉")
                    # 重置內部狀態
                    print("🔧 重置所有內部交易狀態...")
                    self.position_size = 0
                    self.entry_price = 0
                    self.long_entry_price = None
                    self.long_peak = None
                    self.long_trail_stop_price = None
                    self.is_long_trail_active = False
                    self.short_entry_price = None
                    self.short_trough = None
                    self.short_trail_stop_price = None
                    self.is_short_trail_active = False
                    self.save_state()
                    return False
            else:
                print(f"✅ 確認持倉: {actual_position:.5f} ETH")
                break
        
        if actual_position == 0:
            print("❌ 多次查詢後仍顯示無持倉，可能存在同步問題")
            return False

        # 使用實際持倉數量進行平倉
        abs_pos_size = abs(actual_position)
        order = None

        print(f"🔄 準備平倉: 實際持倉 {actual_position:.5f} ETH")

        if actual_position > 0: # 平多單
            print(f"📉 平多單: {actual_position:.5f} {self.symbol} @ ${current_close:.2f}")
            order = self._place_order('sell', abs_pos_size, 'market')
        elif actual_position < 0: # 平空單
            print(f"📈 平空單: {abs_pos_size:.5f} {self.symbol} @ ${current_close:.2f}")
            order = self._place_order('buy', abs_pos_size, 'market')

        if order:
            print(f"✅ 平倉訂單已提交: {order.get('id', 'N/A')}")
            
            # 等待並確認平倉結果
            print(f"⏳ 等待3秒後確認平倉結果...")
            time.sleep(3)
            
            # 確認平倉是否成功
            confirmation_retries = 3
            final_position = None
            
            for confirm_attempt in range(confirmation_retries):
                print(f"🔍 確認嘗試 {confirm_attempt+1}/{confirmation_retries}: 查詢平倉後持倉...")
                final_position = self._get_current_position_size()
                
                if final_position == 0:
                    print(f"✅ 平倉成功確認：持倉已清零")
                    break
                else:
                    print(f"⚠️ 平倉可能未完成，剩餘持倉: {final_position:.5f}")
                    if confirm_attempt < confirmation_retries - 1:
                        time.sleep(2)
            
            if final_position != 0:
                print(f"❌ 平倉確認失敗，剩餘持倉: {final_position:.5f}")
                self.trade_log.append({
                    'time': datetime.now().isoformat(), 
                    'type': 'ERROR_CLOSE_FAILED', 
                    'remaining_position': final_position,
                    'order_id': order.get('id', 'N/A')
                })
                return False
            
            # 計算盈虧
            entry_price_for_calc = self.long_entry_price if actual_position > 0 else self.short_entry_price
            if entry_price_for_calc is not None and entry_price_for_calc > 0:
                if actual_position > 0:
                    profit_loss = (current_close - entry_price_for_calc) * actual_position
                else:
                    profit_loss = (entry_price_for_calc - current_close) * abs(actual_position)
                print(f"💰 平倉盈虧: ${profit_loss:.2f} USDT")
            else:
                profit_loss = 0
                print("⚠️ 無進場價格記錄，無法計算精確盈虧")

            # 更新狀態
            self.current_capital = self._get_free_balance() # 平倉後再次更新資金
            self.position_size = 0
            self.entry_price = 0
            self.long_entry_price = None
            self.long_peak = None
            self.long_trail_stop_price = None
            self.is_long_trail_active = False
            self.short_entry_price = None
            self.short_trough = None
            self.short_trail_stop_price = None
            self.is_short_trail_active = False

            self.trade_log.append({
                'time': datetime.now().isoformat(), 'type': 'EXIT_REAL', 'price': current_close, 'profit_loss': profit_loss,
                'current_position_size': self.position_size, 'current_capital': self.current_capital,
                'order_id': order.get('id', 'N/A')
            })
            self.save_state() # 平倉後保存狀態
            print(f"✅ 平倉完成，狀態已重置")
            return True
        else:
            print(f"❌ 平倉失敗，訂單未成功")
            return False

    # --- 新增：保存策略狀態到 JSON 檔案 ---
    def save_state(self):
        state = {
            'position_size': self.position_size,
            'entry_price': self.entry_price,
            'long_entry_price': self.long_entry_price,
            'long_peak': self.long_peak,
            'long_trail_stop_price': self.long_trail_stop_price,
            'is_long_trail_active': self.is_long_trail_active,
            'short_entry_price': self.short_entry_price,
            'short_trough': self.short_trough,
            'short_trail_stop_price': self.short_trail_stop_price,
            'is_short_trail_active': self.is_short_trail_active,
            'peak_capital': self.peak_capital,
            'max_drawdown': self.max_drawdown,
            'current_capital': self.current_capital, # 備份，雖然會實時查詢
            # trade_log 不建議保存所有歷史，只保存關鍵交易狀態
        }
        try:
            with open(STATE_FILE, 'w') as f:
                json.dump(state, f, indent=4)
            print(f"策略狀態已保存到 {STATE_FILE}")
            return True
        except Exception as e:
            print(f"保存策略狀態失敗: {e}")
            return False

    # --- 新增：從 JSON 檔案加載策略狀態 ---
    def load_state(self):
        if not os.path.exists(STATE_FILE):
            return False
        try:
            with open(STATE_FILE, 'r') as f:
                state = json.load(f)
            
            self.position_size = state.get('position_size', 0)
            self.entry_price = state.get('entry_price', 0)
            self.long_entry_price = state.get('long_entry_price')
            self.long_peak = state.get('long_peak')
            self.long_trail_stop_price = state.get('long_trail_stop_price')
            self.is_long_trail_active = state.get('is_long_trail_active', False)
            self.short_entry_price = state.get('short_entry_price')
            self.short_trough = state.get('short_trough')
            self.short_trail_stop_price = state.get('short_trail_stop_price')
            self.is_short_trail_active = state.get('is_short_trail_active', False)
            self.peak_capital = state.get('peak_capital', self._get_free_balance()) # 如果檔案中沒有，則初始化
            self.max_drawdown = state.get('max_drawdown', 0.0)
            self.current_capital = state.get('current_capital', self._get_free_balance())
            
            return True
        except Exception as e:
            print(f"加載策略狀態失敗: {e}")
            return False

    def process_bar(self, current_bar):
        current_time = current_bar.name
        current_close = current_bar['close']
        current_high = current_bar['high']
        current_low = current_bar['low']
        current_adx = current_bar['adx']

        # 檢查關鍵數據是否為None
        if current_close is None or current_high is None or current_low is None:
            print(f"❌ 價格數據不完整: close={current_close}, high={current_high}, low={current_low}")
            return

        if current_adx is None:
            print(f"❌ ADX數據不完整: {current_adx}")
            return

        if pd.isna(current_bar['ema90']) or pd.isna(current_bar['ema200']) or pd.isna(current_adx):
            print(f"數據不足以計算指標在 {current_time}，跳過。")
            return

        # === 📊 關鍵指標報告 ===
        print(f"\n{'='*60}")
        print(f"📊 【{current_time} 4小時K線】技術指標分析報告")
        print(f"{'='*60}")
        
        # 價格資訊
        print(f"💰 價格資訊:")
        print(f"   開盤價: ${current_bar['open']:.2f}")
        print(f"   最高價: ${current_high:.2f}")
        print(f"   最低價: ${current_low:.2f}")
        print(f"   收盤價: ${current_close:.2f}")
        
        # EMA 指標
        print(f"📈 EMA 移動平均線:")
        print(f"   EMA90:  ${current_bar['ema90']:.2f}")
        print(f"   EMA200: ${current_bar['ema200']:.2f}")
        print(f"   EMA排列: {'多頭排列' if current_bar['ema90'] > current_bar['ema200'] else '空頭排列'}")
        
        # ADX 趨勢強度
        print(f"⚡ ADX 趨勢強度:")
        print(f"   ADX: {current_adx:.2f}")
        print(f"   +DI: {current_bar['plus_di']:.2f}")
        print(f"   -DI: {current_bar['minus_di']:.2f}")
        print(f"   趨勢強度: {'強勢' if current_adx > self.adx_threshold else '弱勢'} (閾值: {self.adx_threshold})")
        
        # MACD 指標
        print(f"🌊 MACD 動量指標:")
        print(f"   MACD線: {current_bar['macd']:.4f}")
        print(f"   信號線: {current_bar['macd_signal']:.4f}")
        print(f"   MACD柱: {current_bar['macd_histogram']:.4f}")
        print(f"   MACD趨勢: {'向上' if current_bar['macd_histogram'] > 0 else '向下'}")
        
        # RSI 指標
        print(f"🎯 RSI 相對強弱:")
        print(f"   RSI: {current_bar['rsi']:.2f}")
        rsi_status = "超買" if current_bar['rsi'] > 70 else "超賣" if current_bar['rsi'] < 30 else "正常"
        print(f"   狀態: {rsi_status}")
        
        # 進場條件檢查
        print(f"🔍 進場條件分析:")
        
        # 多單條件
        long_condition_price = current_close > current_bar['ema90']
        long_condition_low = current_low > current_bar['ema90']
        long_condition_trend = current_close > current_bar['ema200']
        long_condition_strength = current_adx > self.adx_threshold
        
        print(f"   📈 多單條件:")
        print(f"      ✓ 收盤價 > EMA90: {long_condition_price} ({current_close:.2f} > {current_bar['ema90']:.2f})")
        print(f"      ✓ 最低價 > EMA90: {long_condition_low} ({current_low:.2f} > {current_bar['ema90']:.2f})")
        print(f"      ✓ 收盤價 > EMA200: {long_condition_trend} ({current_close:.2f} > {current_bar['ema200']:.2f})")
        print(f"      ✓ ADX > 閾值: {long_condition_strength} ({current_adx:.2f} > {self.adx_threshold})")
        
        long_entry_ready = long_condition_price and long_condition_low and long_condition_trend and long_condition_strength
        print(f"      🎯 多單進場: {'✅ 滿足' if long_entry_ready else '❌ 不滿足'}")
        
        # 空單條件
        short_condition_price = current_close < current_bar['ema90']
        short_condition_high = current_high < current_bar['ema90']
        short_condition_trend = current_close < current_bar['ema200']
        short_condition_strength = current_adx > self.adx_threshold
        
        print(f"   📉 空單條件:")
        print(f"      ✓ 收盤價 < EMA90: {short_condition_price} ({current_close:.2f} < {current_bar['ema90']:.2f})")
        print(f"      ✓ 最高價 < EMA90: {short_condition_high} ({current_high:.2f} < {current_bar['ema90']:.2f})")
        print(f"      ✓ 收盤價 < EMA200: {short_condition_trend} ({current_close:.2f} < {current_bar['ema200']:.2f})")
        print(f"      ✓ ADX > 閾值: {short_condition_strength} ({current_adx:.2f} > {self.adx_threshold})")
        
        short_entry_ready = short_condition_price and short_condition_high and short_condition_trend and short_condition_strength
        print(f"      🎯 空單進場: {'✅ 滿足' if short_entry_ready else '❌ 不滿足'}")
        
        print(f"{'='*60}")
        
        # 詳細持倉和盈虧分析
        if self.position_size != 0:
            if self.position_size > 0:  # 多單
                entry_price = self.long_entry_price
                position_type = "多單"
                current_profit_usd = (current_close - entry_price) * self.position_size if entry_price else 0
                current_profit_percent = ((current_close - entry_price) / entry_price * 100) if entry_price else 0
                
                print(f"📋 當前持倉狀態: {position_type} {self.position_size} ETH")
                print(f"💰 持倉詳細資訊:")
                print(f"   進場價格: ${entry_price:.2f}" if entry_price else "   進場價格: 未記錄")
                print(f"   當前價格: ${current_close:.2f}")
                print(f"   持倉市值: ${current_close * self.position_size:.2f} USDT")
                print(f"   未實現盈虧: ${current_profit_usd:.2f} USDT ({current_profit_percent:+.2f}%)")
                
                # 停損價格分析
                if entry_price:
                    fixed_stop = entry_price * (1 - self.long_fixed_stop_loss_percent)
                    trail_stop = self.long_trail_stop_price
                    
                    print(f"🛡️ 停損設置:")
                    print(f"   固定停損價: ${fixed_stop:.2f} (風險: {((fixed_stop - current_close) / current_close * 100):+.2f}%)")
                    if trail_stop:
                        print(f"   移動停損價: ${trail_stop:.2f} (風險: {((trail_stop - current_close) / current_close * 100):+.2f}%)")
                        print(f"   移動停損狀態: {'✅ 已激活' if self.is_long_trail_active else '❌ 未激活'}")
                    else:
                        print(f"   移動停損價: 未設置")
                        activation_price = entry_price * (1 + self.long_trailing_activate_profit_percent)
                        print(f"   激活所需價格: ${activation_price:.2f} (需上漲 {((activation_price - current_close) / current_close * 100):+.2f}%)")
                        
            else:  # 空單
                entry_price = self.short_entry_price
                position_type = "空單"
                abs_position = abs(self.position_size)
                current_profit_usd = (entry_price - current_close) * abs_position if entry_price else 0
                current_profit_percent = ((entry_price - current_close) / entry_price * 100) if entry_price else 0
                
                print(f"📋 當前持倉狀態: {position_type} {abs_position} ETH")
                print(f"💰 持倉詳細資訊:")
                print(f"   進場價格: ${entry_price:.2f}" if entry_price else "   進場價格: 未記錄")
                print(f"   當前價格: ${current_close:.2f}")
                print(f"   持倉市值: ${current_close * abs_position:.2f} USDT")
                print(f"   未實現盈虧: ${current_profit_usd:.2f} USDT ({current_profit_percent:+.2f}%)")
                
                # 停損價格分析
                if entry_price:
                    fixed_stop = entry_price * (1 + self.short_fixed_stop_loss_percent)
                    trail_stop = self.short_trail_stop_price
                    
                    print(f"🛡️ 停損設置:")
                    print(f"   固定停損價: ${fixed_stop:.2f} (風險: {((current_close - fixed_stop) / current_close * 100):+.2f}%)")
                    if trail_stop:
                        print(f"   移動停損價: ${trail_stop:.2f} (風險: {((current_close - trail_stop) / current_close * 100):+.2f}%)")
                        print(f"   移動停損狀態: {'✅ 已激活' if self.is_short_trail_active else '❌ 未激活'}")
                    else:
                        print(f"   移動停損價: 未設置")
                        activation_price = entry_price * (1 - self.short_trailing_activate_profit_percent)
                        print(f"   激活所需價格: ${activation_price:.2f} (需下跌 {((current_close - activation_price) / current_close * 100):+.2f}%)")
        else:
            print(f"📋 當前持倉狀態: 無持倉")
            
        print(f"💵 帳戶資金: {self.current_capital:.2f} USDT")
        print(f"📊 資金高峰: {self.peak_capital:.2f} USDT")
        if self.max_drawdown > 0:
            print(f"📉 最大回撤: {self.max_drawdown*100:.2f}%")
        print(f"{'='*60}\n") 

        strong_trend = current_adx > self.adx_threshold

        # --- 更新當前資金和持倉狀態 (每次處理K線前都實時查詢) ---
        self.current_capital = self._get_free_balance()
        self.position_size = self._get_current_position_size()
        
        market = self.exchange.market(self.symbol)
        amount_precision = market['precision']['amount']
        min_amount = market['limits']['amount']['min'] if 'amount' in market['limits'] else 0.001
        
        # 統一帳戶合約交易：詳細的資金和交易量計算
        print(f"📊 交易量計算詳情:")
        print(f"   可用資金: {self.current_capital:.2f} USDT")
        print(f"   使用比例: {self.default_qty_percent}%")
        print(f"   當前價格: {current_close:.2f} USDT")

        # 使用完整的設定資金比例
        trade_qty_usd = self.current_capital * self.default_qty_percent / 100
        print(f"   實際使用比例: {self.default_qty_percent:.1f}%")
        trade_qty_unrounded = trade_qty_usd / current_close

        # ETH只能下單到小數點後兩位，使用無條件捨去
        import math
        trade_qty = math.floor(trade_qty_unrounded * 100) / 100

        print(f"   計劃交易金額: {trade_qty_usd:.2f} USDT")
        print(f"   計劃交易數量: {trade_qty_unrounded:.6f} ETH")
        print(f"   無條件捨去到2位小數: {trade_qty:.2f} ETH")
        print(f"   最小交易量: {min_amount} ETH")
        print(f"   交易量精度: {amount_precision}")

        # 確保trade_qty是數字類型
        try:
            trade_qty = float(trade_qty)
        except (ValueError, TypeError):
            print(f"❌ 交易數量轉換失敗: {trade_qty}，設為0")
            trade_qty = 0

        if trade_qty < min_amount:
            print(f"❌ 計算出的交易數量 {trade_qty:.6f} 小於最小交易量 {min_amount:.6f}，跳過交易。")
            trade_qty = 0
        else:
            print(f"✅ 交易數量符合要求: {trade_qty:.6f} ETH")

        # --- 處理多單邏輯 ---
        # 這裡需要重複 long_entry_condition 的判斷，因為它是根據當前K線數據來判斷的。
        long_entry_condition = (
            current_close > current_bar['ema90'] and
            current_low > current_bar['ema90'] and
            current_close > current_bar['ema200'] and
            strong_trend
        )
        if self.position_size == 0:
            if long_entry_condition and float(trade_qty) > 0:
                print(f"{current_time} - 觸發多單進場條件。")
                order = self._place_order('buy', trade_qty, 'market')
                if order and order['status'] == 'closed':
                    self.position_size = order['filled']
                    self.entry_price = order['price']
                    self.long_entry_price = self.entry_price
                    self.long_peak = current_high
                    self.long_trail_stop_price = None
                    self.is_long_trail_active = False
                    print(f"多單已進場，數量: {self.position_size:.3f} @ {self.entry_price:.2f}")
                    self.save_state()

        elif self.position_size > 0:
            # 🔧 修正：確保有進場價格才能執行停損邏輯
            if self.long_entry_price is None or self.long_entry_price <= 0:
                print(f"⚠️ 警告：檢測到多單持倉但無進場價格記錄，無法執行停損！")
                print(f"   建議手動檢查持倉或重啟程式以重新同步狀態")
                return

            # 確保long_peak不為None
            if self.long_peak is None:
                self.long_peak = current_high
                print(f"🔧 初始化多單峰值: ${self.long_peak:.2f}")
            else:
                self.long_peak = max(self.long_peak, current_high)

            # 計算當前盈虧百分比用於調試
            current_profit_percent = (current_close - self.long_entry_price) / self.long_entry_price
            print(f"📊 多單狀態: 進場價${self.long_entry_price:.2f}, 當前價${current_close:.2f}, 盈虧{current_profit_percent*100:.2f}%")

            if not self.is_long_trail_active and \
               current_close > self.long_entry_price * (1 + self.long_trailing_activate_profit_percent):
                self.long_trail_stop_price = self.long_entry_price * (1 + self.long_trailing_min_profit_percent)
                self.is_long_trail_active = True
                print(f"{current_time} - 多單移動止損已激活。初始止損價: ${self.long_trail_stop_price:.2f}")
                self.save_state()

            if self.is_long_trail_active and self.long_peak is not None:
                new_trail_stop = self.long_peak * (1 - self.long_trailing_pullback_percent)
                self.long_trail_stop_price = max(
                    self.long_trail_stop_price if self.long_trail_stop_price is not None else 0,
                    new_trail_stop
                )
                print(f"📊 移動止損更新: 峰值${self.long_peak:.2f}, 止損價${self.long_trail_stop_price:.2f}")
                self.save_state()

            # 計算停損價格
            long_fixed_stop_loss_price = self.long_entry_price * (1 - self.long_fixed_stop_loss_percent)
            print(f"📊 固定止損價: ${long_fixed_stop_loss_price:.2f}")

            # 停損觸發條件 - 使用收盤價判斷，添加 None 檢查
            long_trail_stop_triggered = (
                self.is_long_trail_active and 
                self.long_trail_stop_price is not None and 
                current_close <= self.long_trail_stop_price
            )
            long_fixed_stop_loss_triggered = current_close <= long_fixed_stop_loss_price

            # 添加詳細的停損檢查日誌
            print(f"📊 多單停損檢查:")
            print(f"   移動停損激活: {self.is_long_trail_active}")
            print(f"   移動停損價格: {self.long_trail_stop_price}")
            print(f"   固定停損價格: {long_fixed_stop_loss_price:.2f}")
            print(f"   當前收盤價: {current_close:.2f}")

            if long_trail_stop_triggered:
                print(f"🚨 移動止損觸發: 當前收盤價${current_close:.2f} <= 移動止損價${self.long_trail_stop_price:.2f}")
            if long_fixed_stop_loss_triggered:
                print(f"🚨 固定止損觸發: 當前收盤價${current_close:.2f} <= 固定止損價${long_fixed_stop_loss_price:.2f}")

            if long_trail_stop_triggered or long_fixed_stop_loss_triggered:
                exit_reason = "TRAIL_STOP" if long_trail_stop_triggered else "FIXED_STOP"
                
                print(f"\n🚨 === 多單平倉觸發 ===")
                print(f"時間: {current_time}")
                print(f"觸發原因: {exit_reason}")
                print(f"當前價格: ${current_close:.2f}")
                print(f"持倉量: {self.position_size}")
                print(f"進場價: ${self.long_entry_price:.2f}")
                
                close_success = self._close_position(current_close)
                if not close_success:
                    print(f"❌ 多單平倉失敗，請檢查")
                else:
                    print(f"✅ 多單平倉完成")

        # --- 處理空單邏輯 ---
        # 這裡需要重複 short_entry_condition 的判斷，因為它是根據當前K線數據來判斷的。
        short_entry_condition = (
            current_close < current_bar['ema90'] and
            current_high < current_bar['ema90'] and
            current_close < current_bar['ema200'] and
            strong_trend
        )
        if self.position_size == 0:
            if short_entry_condition and float(trade_qty) > 0:
                print(f"{current_time} - 觸發空單進場條件。")
                order = self._place_order('sell', trade_qty, 'market')
                if order and order['status'] == 'closed':
                    self.position_size = -order['filled']
                    self.entry_price = order['price']
                    self.short_entry_price = self.entry_price
                    self.short_trough = current_low
                    self.short_trail_stop_price = None
                    self.is_short_trail_active = False
                    print(f"空單已進場，數量: {abs(self.position_size):.3f} @ {self.entry_price:.2f}")
                    self.save_state()

        elif self.position_size < 0:
            # 🔧 修正：確保有進場價格才能執行停損邏輯
            if self.short_entry_price is None or self.short_entry_price <= 0:
                print(f"⚠️ 警告：檢測到空單持倉但無進場價格記錄，無法執行停損！")
                print(f"   建議手動檢查持倉或重啟程式以重新同步狀態")
                return

            # 確保short_trough不為None
            if self.short_trough is None:
                self.short_trough = current_low
                print(f"🔧 初始化空單谷值: ${self.short_trough:.2f}")
            else:
                self.short_trough = min(self.short_trough, current_low)

            # 計算當前盈虧百分比用於調試
            current_profit_percent = (self.short_entry_price - current_close) / self.short_entry_price
            print(f"📊 空單狀態: 進場價${self.short_entry_price:.2f}, 當前價${current_close:.2f}, 盈虧{current_profit_percent*100:.2f}%")

            if not self.is_short_trail_active and \
               current_close < self.short_entry_price * (1 - self.short_trailing_activate_profit_percent):
                self.short_trail_stop_price = self.short_entry_price * (1 - self.short_trailing_min_profit_percent)
                self.is_short_trail_active = True
                print(f"{current_time} - 空單移動止損已激活。初始止損價: ${self.short_trail_stop_price:.2f}")
                self.save_state()

            if self.is_short_trail_active and self.short_trough is not None:
                new_trail_stop = self.short_trough * (1 + self.short_trailing_pullback_percent)
                self.short_trail_stop_price = min(
                    self.short_trail_stop_price if self.short_trail_stop_price is not None else float('inf'),
                    new_trail_stop
                )
                print(f"📊 移動止損更新: 谷值${self.short_trough:.2f}, 止損價${self.short_trail_stop_price:.2f}")
                self.save_state()

            # 計算停損價格
            short_fixed_stop_loss_price = self.short_entry_price * (1 + self.short_fixed_stop_loss_percent)
            print(f"📊 固定止損價: ${short_fixed_stop_loss_price:.2f}")

            # 停損觸發條件 - 使用收盤價判斷，添加 None 檢查
            short_trail_stop_triggered = (
                self.is_short_trail_active and 
                self.short_trail_stop_price is not None and 
                current_close >= self.short_trail_stop_price
            )
            short_fixed_stop_loss_triggered = current_close >= short_fixed_stop_loss_price

            # 添加詳細的停損檢查日誌
            print(f"📊 空單停損檢查:")
            print(f"   移動停損激活: {self.is_short_trail_active}")
            print(f"   移動停損價格: {self.short_trail_stop_price}")
            print(f"   固定停損價格: {short_fixed_stop_loss_price:.2f}")
            print(f"   當前收盤價: {current_close:.2f}")

            if short_trail_stop_triggered:
                print(f"🚨 移動止損觸發: 當前收盤價${current_close:.2f} >= 移動止損價${self.short_trail_stop_price:.2f}")
            if short_fixed_stop_loss_triggered:
                print(f"🚨 固定止損觸發: 當前收盤價${current_close:.2f} >= 固定止損價${short_fixed_stop_loss_price:.2f}")

            if short_trail_stop_triggered or short_fixed_stop_loss_triggered:
                exit_reason = "TRAIL_STOP" if short_trail_stop_triggered else "FIXED_STOP"
                
                print(f"\n🚨 === 空單平倉觸發 ===")
                print(f"時間: {current_time}")
                print(f"觸發原因: {exit_reason}")
                print(f"當前價格: ${current_close:.2f}")
                print(f"持倉量: {self.position_size}")
                print(f"進場價: ${self.short_entry_price:.2f}")
                
                close_success = self._close_position(current_close)
                if not close_success:
                    print(f"❌ 空單平倉失敗，請檢查")
                else:
                    print(f"✅ 空單平倉完成")

        # --- 更新資金和回撤計算 ---
        try:
            balance_data = self.exchange.fetch_balance()
            total_equity = balance_data['total']['USDT']
            
            self.current_capital = total_equity

            self.peak_capital = max(self.peak_capital, self.current_capital)
            
            if self.peak_capital > 0:
                current_drawdown = (self.peak_capital - self.current_capital) / self.peak_capital
                self.max_drawdown = max(self.max_drawdown, current_drawdown)
        except Exception as e:
            print(f"更新實時資金和回撤失敗: {e}")
        
        self.save_state() # 每處理完一根K線都保存一次狀態，確保最新狀態被記錄

# --- 回測的單次回測邏輯，保持不變 ---
def run_single_backtest(params_tuple):
    """
    執行單一參數組合的回測，並返回關鍵績效指標，但不返回完整的 trade_log。
    接收一個包含所有參數的元組。
    """
    params, df_processed_data, initial_capital_dummy, default_qty_percent = params_tuple # 解包元組
    
    # 回測時，TradingStrategy 內部不會調用 Bybit API，而是使用傳入的 dummy 資金
    strategy = TradingStrategy(
        initial_capital_dummy=initial_capital_dummy, 
        default_qty_percent=default_qty_percent,
        **params 
    ) 
    
    # 手動將初始資金設置為 initial_capital_dummy，因為回測時不會從 Bybit 獲取
    strategy.current_capital = initial_capital_dummy
    strategy.peak_capital = initial_capital_dummy

    for _, row in df_processed_data.iterrows():
        strategy.process_bar(row)

    total_profit = strategy.current_capital - strategy.initial_capital_dummy # 使用 dummy initial_capital 計算
    max_dd_percent = strategy.max_drawdown * 100

    return {
        'params': params,
        'total_profit': total_profit,
        'max_drawdown_percent': max_dd_percent,
    }


# --- 主運行邏輯 (實時交易) ---
def run_live_trading():
    # 使用您找到的最佳參數！
    # 🏆 最佳參數組合 (2020-2025年優化結果)
    # 穩定性評分: 48.38 | 總獲利: 5371.25 USDT | 獲利一致性: 95.45%
    best_params = {
        'adx_threshold': 29,  # 更嚴格的趨勢判斷
        'long_fixed_stop_loss_percent': 0.019,  # 1.9%
        'long_trailing_activate_profit_percent': 0.011,  # 1.1%
        'long_trailing_pullback_percent': 0.054,  # 5.4%
        'long_trailing_min_profit_percent': 0.024,  # 2.4%
        'short_fixed_stop_loss_percent': 0.013,  # 1.3%
        'short_trailing_activate_profit_percent': 0.019,  # 1.9%
        'short_trailing_pullback_percent': 0.018,  # 1.8%
        'short_trailing_min_profit_percent': 0.016  # 1.6%
    }

    # 初始化策略實例 (現在它會查詢 Bybit API 並加載保存的狀態)
    strategy = TradingStrategy(initial_capital_dummy=1000, default_qty_percent=DEFAULT_QTY_PERCENT, **best_params)

    last_kline_timestamp = None

    print("\n--- 開始實時交易 ---")
    while True:
        try:
            # 獲取最新 K 線數據
            df_klines = fetch_bybit_klines(SYMBOL, TIMEFRAME, limit=FETCH_KLINE_LIMIT)

            if df_klines.empty:
                print("未獲取到 K 線數據，等待下一週期...")
                time.sleep(TRADE_SLEEP_SECONDS)
                continue

            df_processed = calculate_indicators(df_klines.copy())
            
            # 確保 df_processed 至少有兩行 (一根完成K線 + 一根當前K線)
            if len(df_processed) < 2:
                print(f"數據不足，至少需要2根完整K線。當前僅有 {len(df_processed)} 根。")
                time.sleep(TRADE_SLEEP_SECONDS)
                continue
                
            current_bar = df_processed.iloc[-2] # 倒數第二根是最新完成的K線
            
            # 如果是第一次運行或有新的K線形成
            if last_kline_timestamp is None or current_bar.name > last_kline_timestamp:
                print(f"\n🔔 檢測到新 4小時 K 線: {current_bar.name}")
                print(f"⏰ 開始技術分析和交易判斷...")
                
                # 將最新完成的 K 線傳入策略進行處理
                strategy.process_bar(current_bar)
                last_kline_timestamp = current_bar.name
            else:
                current_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                next_kline_time = last_kline_timestamp + timedelta(hours=4) if last_kline_timestamp else "未知"
                print(f"⏳ 等待新 4小時 K 線... 當前時間: {current_time_str}")
                print(f"📅 下次K線預計時間: {next_kline_time}")
                print(f"💤 60秒後再次檢查...")
            
            time.sleep(TRADE_SLEEP_SECONDS) # 每隔 X 秒檢查一次
            
        except Exception as e:
            print(f"主循環發生錯誤: {e}")
            time.sleep(TRADE_SLEEP_SECONDS * 2) # 錯誤時等待更久，避免頻繁報錯

# --- 運行回測 / 實時交易模式切換 ---
if __name__ == "__main__":
    # 將 RUN_LIVE 設置為 True 以啟用實時交易模式
    # 將 RUN_LIVE 設置為 False 以執行參數最佳化回測
    RUN_LIVE = True # <--- 在這裡切換模式

    if RUN_LIVE:
        # 在運行實時交易前，請務必確認 API 金鑰已正確設定且有足夠的資金！
        # 這是真實交易，請謹慎！
        print("!!! 警告：即將啟動實時交易模式 !!!")
        
        # 處理非互動模式
        try:
            input("請確認您已理解風險並準備好，按 Enter 鍵繼續...")
        except EOFError:
            print("檢測到非互動模式，自動確認繼續...")
            import time
            time.sleep(2)
        
        run_live_trading()
    else:
        # 回測部分 (現在回測也會從 Bybit API 獲取數據，而非本地 CSV)
        print("--- 載入數據和計算指標 (回測模式，從 Bybit 獲取數據) ---")
        df_data = fetch_bybit_klines(SYMBOL, TIMEFRAME, limit=FETCH_KLINE_LIMIT)

        if not df_data.empty:
            df_processed = calculate_indicators(df_data.copy())

            # 參數最佳化設定 (保持您的範圍不變，但請注意，如果需要，現在也會從 Bybit 獲取數據來進行回測)
            parameters_to_optimize = {
                'adx_threshold': {'min': 15, 'max': 25, 'step': 2, 'type': int}, 
                'long_fixed_stop_loss_percent': {'min': 0.015, 'max': 0.035, 'step': 0.005, 'type': float}, 
                'long_trailing_activate_profit_percent': {'min': 0.015, 'max': 0.035, 'step': 0.005, 'type': float},
                'long_trailing_pullback_percent': {'min': 0.03, 'max': 0.06, 'step': 0.01, 'type': float},
                'long_trailing_min_profit_percent': {'min': 0.005, 'max': 0.025, 'step': 0.005, 'type': float},
                'short_fixed_stop_loss_percent': {'min': 0.015, 'max': 0.035, 'step': 0.005, 'type': float},
                'short_trailing_activate_profit_percent': {'min': 0.015, 'max': 0.035, 'step': 0.005, 'type': float},
                'short_trailing_pullback_percent': {'min': 0.03, 'max': 0.06, 'step': 0.01, 'type': float},
                'short_trailing_min_profit_percent': {'min': 0.005, 'max': 0.025, 'step': 0.005, 'type': float},
            }

            param_value_lists = {}
            for param_name, specs in parameters_to_optimize.items():
                value_list = []
                current_val = specs['min']
                while current_val <= specs['max']:
                    if specs['type'] == int:
                        value_list.append(int(current_val))
                    else: # float
                        value_list.append(round(current_val, 4)) 
                    current_val += specs['step']
                param_value_lists[param_name] = value_list

            keys = param_value_lists.keys()
            all_combinations_raw = list(itertools.product(*param_value_lists.values()))
            
            tasks = []
            for combo in all_combinations_raw:
                current_params_dict = dict(zip(keys, combo))
                tasks.append((current_params_dict, df_processed, 1000, 70)) 

            best_profit = -float('inf') 
            best_params = {}            
            
            print("\n--- 開始參數最佳化 (多核心運算) ---")
            total_combinations = len(tasks)
            print(f"總共需要回測 {total_combinations} 種參數組合。")

            num_cores = os.cpu_count() 
            print(f"您的CPU有 {num_cores} 個邏輯處理器。將使用 {num_cores} 個進程進行平行運算。")
            print("-" * 40)

            from tqdm.contrib.concurrent import process_map # 確保在這裡引入

            results = process_map(run_single_backtest, tasks, max_workers=num_cores, desc="回測進度")
            
            for result in results:
                if result['total_profit'] > best_profit:
                    best_profit = result['total_profit']
                    best_params = result['params'].copy() 
            
            print("\n" + "-" * 40)
            print("--- 參數最佳化結束 ---")
            print("--- 最佳參數組合 ---")
            print(f"最佳總盈虧: {best_profit:.2f} USDT")
            print(f"最佳參數: {best_params}")

            # --- 最佳參數組合的詳細回測報告 (需要手動運行一次以獲取 trade_log) ---
            if best_params: 
                print("\n--- 最佳參數組合的詳細回測報告 (單獨運行以獲取詳細交易日誌) ---")
                
                final_strategy = TradingStrategy(
                    initial_capital_dummy=1000, 
                    default_qty_percent=70,
                    **best_params 
                )
                # 為回測目的，手動設置回測初始資金
                final_strategy.current_capital = 1000
                final_strategy.peak_capital = 1000

                for _, row in df_processed.iterrows():
                    final_strategy.process_bar(row)

                all_exit_trades = [t for t in final_strategy.trade_log if 'EXIT' in t['type'] and '_REAL' not in t['type']]

                long_trades = [t for t in all_exit_trades if t['type'].startswith('LONG_EXIT')]
                short_trades = [t for t in all_exit_trades if t['type'].startswith('SHORT_EXIT')]

                def calc_stats(trades):
                    total = len(trades)
                    wins = sum(1 for t in trades if t['profit_loss'] > 0)
                    losses = total - wins
                    win_rate = (wins / total * 100) if total > 0 else 0
                    return total, wins, losses, win_rate

                long_total, long_wins, long_losses, long_win_rate = calc_stats(long_trades)
                short_total, short_wins, short_losses, short_win_rate = calc_stats(short_trades)

                total_trades = long_total + short_total
                total_wins = long_wins + short_wins
                total_win_rate = (total_wins / total_trades * 100) if total_trades > 0 else 0

                # 這裡的 trail_exits 和 fixed_exits 需要從回測日誌中提取，而不是實時日誌
                # 如果回測時沒有 reason 欄位，這部分可能不準確
                # 假設回測時 'reason' 欄位有正確設置
                trail_exits = [t for t in final_strategy.trade_log if t.get('reason') == 'TRAIL_STOP']
                fixed_exits = [t for t in final_strategy.trade_log if t.get('reason') == 'FIXED_STOP']
                print(f"\n--- ⛓️ 出場類型統計 (最佳參數) ---")
                print(f"🧲 移動停損出場次數：{len(trail_exits)}")
                print(f"🧱 固定止損出場次數：{len(fixed_exits)}")

                print("\n--- 📊 交易統計報告 (最佳參數) ---")
                print(f"💰 初始資金：1000.00 USDT") 
                print(f"📈 多單次數：{long_total}｜勝場：{long_wins}｜敗場：{long_losses}｜勝率：{long_win_rate:.2f}%")
                print(f"📉 空單次數：{short_total}｜勝場：{short_wins}｜敗場：{short_losses}｜勝率：{short_win_rate:.2f}%")
                print(f"🔁 總交易次數：{total_trades}｜總勝率：{total_win_rate:.2f}%")
                print(f"🏁 最終資金：{final_strategy.current_capital:.2f} USDT") 
                print(f"📊 總盈虧：{final_strategy.current_capital - 1000:.2f} USDT")
                
                if final_strategy.max_drawdown > 0:
                    print(f"📉 最大資金回撤 (Max Drawdown)：{final_strategy.max_drawdown * 100:.2f}%")
                else:
                    print("📈 在此回測期間內，資金持續增長，未發生明顯回撤。")
            else:
                print("未能找到最佳參數組合，請檢查參數設定範圍。")

        else:
            print("無法進行回測，因為沒有數據。")