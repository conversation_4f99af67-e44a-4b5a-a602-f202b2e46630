import ccxt
import pandas as pd
import time
from datetime import datetime, timedelta
import os

class ETHHistoricalDataFetcher:
    """ETH歷史數據提取器 - 獲取更大範圍的4小時K線數據用於回測"""
    
    def __init__(self, exchange_name='binance'):
        """初始化交易所連接"""
        if exchange_name == 'binance':
            self.exchange = ccxt.binance({
                'sandbox': False,  # 使用實盤數據
                'rateLimit': 1200,  # 限制請求頻率
                'enableRateLimit': True,
            })
        elif exchange_name == 'bybit':
            self.exchange = ccxt.bybit({
                'sandbox': False,  # 使用實盤數據
                'rateLimit': 1200,  # 限制請求頻率
                'enableRateLimit': True,
            })
        else:
            raise ValueError(f"不支援的交易所: {exchange_name}")

        self.exchange_name = exchange_name
        print(f"使用交易所: {exchange_name.upper()}")
        
    def fetch_historical_data(self, symbol='ETH/USDT', timeframe='4h', 
                            start_date='2020-01-01', end_date=None, 
                            output_file='eth_usdt_4h_extended_data.csv'):
        """
        獲取歷史K線數據
        
        Args:
            symbol: 交易對，默認 'ETH/USDT'
            timeframe: 時間週期，默認 '4h'
            start_date: 開始日期，格式 'YYYY-MM-DD'
            end_date: 結束日期，格式 'YYYY-MM-DD'，默認為今天
            output_file: 輸出檔案名
        """
        
        # 轉換日期
        start_timestamp = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
        if end_date:
            end_timestamp = int(datetime.strptime(end_date, '%Y-%m-%d').timestamp() * 1000)
        else:
            end_timestamp = int(datetime.now().timestamp() * 1000)
            
        print(f"開始獲取 {symbol} {timeframe} 數據")
        print(f"時間範圍: {start_date} 到 {end_date or '今天'}")
        
        all_data = []
        current_timestamp = start_timestamp
        
        # 每次請求的數據量限制（幣安限制每次最多1000根K線，Bybit也是1000）
        limit = 1000
        
        while current_timestamp < end_timestamp:
            try:
                print(f"正在獲取數據: {datetime.fromtimestamp(current_timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 獲取數據
                ohlcv = self.exchange.fetch_ohlcv(
                    symbol=symbol,
                    timeframe=timeframe,
                    since=current_timestamp,
                    limit=limit
                )
                
                if not ohlcv:
                    print("沒有更多數據")
                    break
                    
                # 轉換為DataFrame
                df_batch = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df_batch['timestamp'] = pd.to_datetime(df_batch['timestamp'], unit='ms')
                
                all_data.append(df_batch)
                
                # 更新時間戳為最後一根K線的時間 + 1毫秒
                current_timestamp = ohlcv[-1][0] + 1
                
                # 如果獲取的數據少於限制，說明已經到了最新數據
                if len(ohlcv) < limit:
                    break
                    
                # 避免請求過於頻繁
                time.sleep(0.1)
                
            except Exception as e:
                print(f"獲取數據時發生錯誤: {e}")
                print("等待5秒後重試...")
                time.sleep(5)
                continue
        
        if not all_data:
            print("沒有獲取到任何數據")
            return None
            
        # 合併所有數據
        df_all = pd.concat(all_data, ignore_index=True)
        
        # 去除重複數據並排序
        df_all = df_all.drop_duplicates(subset=['timestamp']).sort_values('timestamp')
        
        # 重置索引
        df_all = df_all.reset_index(drop=True)
        
        print(f"總共獲取 {len(df_all)} 根K線")
        print(f"數據範圍: {df_all['timestamp'].min()} 到 {df_all['timestamp'].max()}")
        
        # 保存到CSV
        df_all.to_csv(output_file, index=False)
        print(f"數據已保存到: {output_file}")
        
        return df_all
    
    def get_data_summary(self, df):
        """獲取數據摘要信息"""
        if df is None or df.empty:
            return
            
        print(f"\n=== 數據摘要 ===")
        print(f"總K線數量: {len(df)}")
        print(f"時間範圍: {df['timestamp'].min()} 到 {df['timestamp'].max()}")
        print(f"價格範圍: ${df['low'].min():.2f} - ${df['high'].max():.2f}")
        print(f"平均成交量: {df['volume'].mean():.2f}")
        
        # 計算數據覆蓋的年數
        time_span = df['timestamp'].max() - df['timestamp'].min()
        years = time_span.days / 365.25
        print(f"數據覆蓋: {years:.1f} 年")
        
        # 每年大約的K線數量（4小時週期）
        expected_bars_per_year = 365.25 * 24 / 4  # 約2191根
        print(f"預期每年K線數: {expected_bars_per_year:.0f}")
        print(f"實際每年平均K線數: {len(df) / years:.0f}")

def main():
    """主函數 - 提供多種數據獲取選項"""

    print("=== ETH歷史數據提取器 ===")
    print("選擇交易所:")
    print("1. 幣安 (Binance) - 推薦，歷史數據更完整")
    print("2. Bybit")

    exchange_choice = input("請選擇交易所 (1-2): ").strip()

    if exchange_choice == '1':
        exchange_name = 'binance'
    elif exchange_choice == '2':
        exchange_name = 'bybit'
    else:
        print("無效選擇，使用預設幣安")
        exchange_name = 'binance'

    fetcher = ETHHistoricalDataFetcher(exchange_name)

    print("\n選擇數據範圍:")
    print("1. 最近2年數據 (2022-01-01 至今)")
    print("2. 最近3年數據 (2021-01-01 至今)")
    print("3. 最近5年數據 (2019-01-01 至今)")
    print("4. 最近8年數據 (2017-01-01 至今) - 推薦用於完整回測")
    print("5. 自定義範圍")
    
    choice = input("請選擇 (1-5): ").strip()

    if choice == '1':
        start_date = '2022-01-01'
        output_file = f'lookingforthebest/eth_usdt_4h_2years_{exchange_name}.csv'
    elif choice == '2':
        start_date = '2021-01-01'
        output_file = f'lookingforthebest/eth_usdt_4h_3years_{exchange_name}.csv'
    elif choice == '3':
        start_date = '2019-01-01'
        output_file = f'lookingforthebest/eth_usdt_4h_5years_{exchange_name}.csv'
    elif choice == '4':
        start_date = '2017-01-01'
        output_file = f'lookingforthebest/eth_usdt_4h_8years_{exchange_name}.csv'
    elif choice == '5':
        start_date = input("請輸入開始日期 (YYYY-MM-DD): ").strip()
        end_date = input("請輸入結束日期 (YYYY-MM-DD，留空為今天): ").strip()
        if not end_date:
            end_date = None
        output_file = input("請輸入輸出檔案名 (如: eth_custom_data.csv): ").strip()
    else:
        print("無效選擇")
        return
    
    # 確保輸出目錄存在
    os.makedirs(os.path.dirname(output_file) if os.path.dirname(output_file) else '.', exist_ok=True)
    
    # 獲取數據
    if choice == '5':
        df = fetcher.fetch_historical_data(
            start_date=start_date,
            end_date=end_date,
            output_file=output_file
        )
    else:
        df = fetcher.fetch_historical_data(
            start_date=start_date,
            output_file=output_file
        )
    
    # 顯示摘要
    fetcher.get_data_summary(df)
    
    print(f"\n✅ 完成！數據已保存到: {output_file}")
    print("您現在可以修改 fast_stability_optimizer.py 中的數據檔案路徑來使用新的數據進行回測。")

    # 提供快速修改優化器的建議
    print(f"\n📝 修改建議:")
    print(f"在 fast_stability_optimizer.py 的第12行，將:")
    print(f"df = pd.read_csv('lookingforthebest/eth_usdt_4h_data.csv', ...)")
    print(f"改為:")
    print(f"df = pd.read_csv('{output_file}', ...)")

def quick_data_check(file_path):
    """快速檢查數據檔案的基本信息"""
    try:
        df = pd.read_csv(file_path)
        print(f"\n=== {file_path} 數據檢查 ===")
        print(f"總行數: {len(df)}")
        print(f"列名: {list(df.columns)}")
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            print(f"時間範圍: {df['timestamp'].min()} 到 {df['timestamp'].max()}")
        if 'close' in df.columns:
            print(f"價格範圍: ${df['close'].min():.2f} - ${df['close'].max():.2f}")
        print("✅ 數據檔案格式正確")
    except Exception as e:
        print(f"❌ 檢查數據檔案時發生錯誤: {e}")

if __name__ == "__main__":
    import sys

    # 如果提供了檔案路徑參數，則檢查該檔案
    if len(sys.argv) > 1 and sys.argv[1] == 'check':
        if len(sys.argv) > 2:
            quick_data_check(sys.argv[2])
        else:
            print("請提供要檢查的檔案路徑")
            print("用法: python eth_historical_data_fetcher.py check <檔案路徑>")
    else:
        main()
